import { takeScreenshot, uploadScreenshot } from './screenshotUtils';

// Mock the global fetch
global.fetch = jest.fn();

// Mock canvas and context
const mockContext = {
  drawImage: jest.fn()
};

const mockCanvas = {
  getContext: jest.fn(() => mockContext),
  toBlob: jest.fn(callback => callback(new Blob(['test'], { type: 'image/png' })))
};

// Mock document.createElement
document.createElement = jest.fn(() => mockCanvas);

describe('screenshotUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    global.fetch.mockClear();
  });

  describe('takeScreenshot', () => {
    it('should take a screenshot from the renderer', async () => {
      const mockRenderer = {
        domElement: {
          width: 800,
          height: 600,
          toBlob: null // No toBlob method, will use fallback
        },
        scene: {},
        camera: {},
        render: jest.fn()
      };

      const result = await takeScreenshot(mockRenderer);

      // Should try to render the scene
      expect(mockRenderer.render).toHaveBeenCalledWith(mockRenderer.scene, mockRenderer.camera);

      // Should use fallback method
      expect(document.createElement).toHaveBeenCalledWith('canvas');
      expect(mockCanvas.getContext).toHaveBeenCalledWith('2d');
      expect(mockContext.drawImage).toHaveBeenCalledWith(mockRenderer.domElement, 0, 0, 800, 600);
      expect(mockCanvas.toBlob).toHaveBeenCalled();
      expect(result).toBeInstanceOf(Blob);
    });

    it('should use provided width and height', async () => {
      const mockRenderer = {
        domElement: {
          width: 800,
          height: 600
        }
      };

      await takeScreenshot(mockRenderer, 400, 300);

      expect(mockCanvas.width).toBe(400);
      expect(mockCanvas.height).toBe(300);
      expect(mockContext.drawImage).toHaveBeenCalledWith(mockRenderer.domElement, 0, 0, 400, 300);
    });
  });

  describe('uploadScreenshot', () => {
    it('should upload a screenshot to the server', async () => {
      const mockBlob = new Blob(['test'], { type: 'image/png' });
      const mockPatientId = '12345';

      // Mock successful response
      global.fetch.mockResolvedValueOnce({
        ok: true,
        text: jest.fn().mockResolvedValueOnce(JSON.stringify({
          status: "success",
          message: "File uploaded successfully",
          data: [{
            Location: "https://example.com/screenshots/12345.png",
            Key: "screenshots/12345.png",
            Bucket: "upod"
          }]
        }))
      });

      const result = await uploadScreenshot(mockBlob, mockPatientId);

      expect(global.fetch).toHaveBeenCalledWith(
        'https://api.modularcx.link/upod-medical/s3/upload',
        expect.objectContaining({
          method: 'POST',
          body: expect.any(FormData),
          redirect: 'follow'
        })
      );

      expect(result).toBe(JSON.stringify({
        status: "success",
        message: "File uploaded successfully",
        data: [{
          Location: "https://example.com/screenshots/12345.png",
          Key: "screenshots/12345.png",
          Bucket: "upod"
        }]
      }));
    });

    it('should handle upload errors', async () => {
      const mockBlob = new Blob(['test'], { type: 'image/png' });

      // Mock failed response
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 500
      });

      await expect(uploadScreenshot(mockBlob)).rejects.toThrow('Upload failed with status: 500');
    });
  });
});
