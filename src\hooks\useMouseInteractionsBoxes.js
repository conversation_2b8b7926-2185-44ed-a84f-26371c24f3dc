import { useRef, useState, useEffect, useCallback } from "react";
import * as THREE from "three";
import { createSurfaceHighlightMaterial } from "../utils/modelUtils";

export const useMouseInteractionsBoxes = ({
  camera,
  gl,
  chartingBoxesRef,
  setHoveredTooth,
  setSelectedTooth,
  setSelectedSurfaces,
  openRightClickModal,
  keyDown,
  selectedTreatment,
}) => {
  const raycasterRef = useRef(new THREE.Raycaster());
  const mouseRef = useRef(new THREE.Vector2());
  const prevHoveredRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const lastEventTimeRef = useRef(0);
  const mountedRef = useRef(true);
  const highlightedMeshesRef = useRef(new Set());
  const fadeStates = new Map();
  const selectedSurfacesRef = useRef(new Set());
  const keyDownRef = useRef(null);

  // Reset all highlighted materials when unmounting
  const resetAllHighlightedMeshes = useCallback(() => {
    if (highlightedMeshesRef.current.size > 0) {
      highlightedMeshesRef.current.forEach((mesh) => {
        if (mesh.userData.originalMaterial) {
          mesh.material = mesh.userData.originalMaterial.clone();
        }
      });

      highlightedMeshesRef.current.clear();
    }
  }, []);

  // Get all interactive objects from the surfaces
  const getInteractiveObjects = useCallback(() => {
    if (!chartingBoxesRef || !chartingBoxesRef.current) {
      return [];
    }

    const objects = [];

    // Skip logging on every mouse move
    const shouldLog = Date.now() - lastEventTimeRef.current > 1000;
    if (shouldLog) {
      lastEventTimeRef.current = Date.now();
    }

    chartingBoxesRef.current.forEach((box) => {
      if (box) {
        box.traverse((surface) => {
          if (surface.isMesh && surface.userData.isInteractive) {
            objects.push(surface);
          }
        });
      }
    });

    return objects;
  }, [chartingBoxesRef]);

  const fadeOutAndHide = (box, duration = 200) => {
    const currentState = fadeStates.get(box);
    if (currentState?.fading === "out") return;

    cancelFade(box);

    const startOpacity = box.material.opacity ?? 1;
    const start = performance.now();

    function animate(now) {
      const elapsed = now - start;
      const t = Math.min(elapsed / duration, 1);
      box.material.opacity = startOpacity * (1 - t);

      if (t < 1) {
        const id = requestAnimationFrame(animate);
        fadeStates.set(box, { id, fading: "out" });
      } else {
        box.visible = false;
        fadeStates.delete(box);
      }
    }

    const id = requestAnimationFrame(animate);
    fadeStates.set(box, { id, fading: "out" });
  };

  const fadeInAndShow = (box, duration = 200) => {
    const currentState = fadeStates.get(box);
    if (currentState?.fading === "in") return;

    cancelFade(box);

    box.visible = true;
    box.material.transparent = true;

    const startOpacity = box.material.opacity ?? 0;
    const start = performance.now();

    function animate(now) {
      const elapsed = now - start;
      const t = Math.min(elapsed / duration, 1);
      box.material.opacity = startOpacity + (1 - startOpacity) * t;

      if (t < 1) {
        const id = requestAnimationFrame(animate);
        fadeStates.set(box, { id, fading: "in" });
      } else {
        fadeStates.delete(box);
      }
    }

    const id = requestAnimationFrame(animate);
    fadeStates.set(box, { id, fading: "in" });
  };

  const cancelFade = (box) => {
    const state = fadeStates.get(box);
    if (state) {
      cancelAnimationFrame(state.id);
      fadeStates.delete(box);
    }
  };

  // Handle hover state changes
  const handleHover = useCallback(
    (toothNumber, surfaceName) => {
      if (!mountedRef.current) return;
      if (prevHoveredRef.current === `${toothNumber}_${surfaceName}`) return;

      // Reset previous hover
      if (prevHoveredRef.current) {
        const prevBox = chartingBoxesRef.current.get(
          parseInt(prevHoveredRef.current.split("_")[0]),
        );

        if (prevBox) {
          // Hide previous box
          if (parseInt(prevHoveredRef.current.split("_")[0]) !== toothNumber) {
            fadeOutAndHide(prevBox, 200);
          }
          // Hide previous surface
          prevBox.traverse((surface) => {
            if (
              surface.isMesh &&
              surface.userData.originalMaterial &&
              surface.userData.name === prevHoveredRef.current.split("_")[1]
            ) {
              try {
                // Create a new instance of the material to avoid sharing
                const restoredMaterial =
                  surface.userData.originalMaterial.clone();

                // Ensure all transparency-related properties are properly restored
                if (
                  surface.userData.originalMaterial.transparent !== undefined
                ) {
                  restoredMaterial.transparent =
                    surface.userData.originalMaterial.transparent;
                }
                if (surface.userData.originalMaterial.opacity !== undefined) {
                  restoredMaterial.opacity =
                    surface.userData.originalMaterial.opacity;
                }
                if (
                  surface.userData.originalMaterial.depthWrite !== undefined
                ) {
                  restoredMaterial.depthWrite =
                    surface.userData.originalMaterial.depthWrite;
                }
                if (
                  surface.userData.originalMaterial.renderOrder !== undefined
                ) {
                  restoredMaterial.renderOrder =
                    surface.userData.originalMaterial.renderOrder;
                }
                if (surface.userData.originalMaterial.color !== undefined) {
                  restoredMaterial.color.copy(
                    surface.userData.originalMaterial.color,
                  );
                }

                surface.material = restoredMaterial;
                highlightedMeshesRef.current.delete(surface);
              } catch (e) {
                // Error restoring material
              }
            }
          });
        }
      }

      // Set new hover
      if (toothNumber && surfaceName) {
        const box = chartingBoxesRef.current.get(toothNumber);

        if (box) {
          // Show box
          if (parseInt(prevHoveredRef.current.split("_")[0]) !== toothNumber) {
            fadeInAndShow(box, 200);
          } else {
            box.visible = true;
          }
          // Highlight surface
          box.traverse((surface) => {
            if (surface.isMesh && surface.userData.name === surfaceName) {
              // Set highlight material - create a new one each time
              //
              // Add to tracked set
              highlightedMeshesRef.current.add(surface);
            }
          });
        }
      }

      prevHoveredRef.current = `${toothNumber}_${surfaceName}`;
      // Always update hovered tooth state regardless of material change
      // setHoveredTooth(toothNumber);
    },
    [chartingBoxesRef],
  );

  // Handle mouse move events
  const handleMouseMove = useCallback(
    (event) => {
      if (!mountedRef.current || !isInitialized) return;

      const rect = gl.domElement.getBoundingClientRect();
      mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      raycasterRef.current.setFromCamera(mouseRef.current, camera);

      const interactiveObjects = getInteractiveObjects();

      if (interactiveObjects.length === 0) return;

      const intersects = raycasterRef.current.intersectObjects(
        interactiveObjects,
        true,
      );

      if (intersects.length > 0) {
        const toothNumber = parseInt(intersects[0].object.userData.number);
        const surfaceName = intersects[0].object.userData.name;
        if (toothNumber && surfaceName) {
          handleHover(toothNumber, surfaceName);
        }
      } else {
        handleHover(null);
      }
    },
    [camera, getInteractiveObjects, gl, handleHover, isInitialized],
  );

  // Handle click events
  const handleClick = useCallback(
    (event) => {
      if (!mountedRef.current || !isInitialized) return;

      const rect = gl.domElement.getBoundingClientRect();
      mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      raycasterRef.current.setFromCamera(mouseRef.current, camera);

      const interactiveObjects = getInteractiveObjects();
      if (interactiveObjects.length === 0) return;

      const intersects = raycasterRef.current.intersectObjects(
        interactiveObjects,
        true,
      );

      if (intersects.length > 0) {
        const toothNumber = parseInt(intersects[0].object.userData.number);
        const surfaceName = intersects[0].object.userData.name;
        const box = chartingBoxesRef.current.get(toothNumber);
        if (event.button === 0) {
          // Left click
          // We still want to handle selection for all teeth, even Decay/filling ones
          setSelectedTooth((prev) =>
            prev === toothNumber ? null : toothNumber,
          );
          if (
            keyDownRef.current === "cntrl" &&
            !selectedTreatment.full_tooth_treatment
          ) {
            if (
              selectedSurfacesRef.current.has(`${toothNumber}_${surfaceName}`)
            ) {
              // Remove from selected surfaces
              selectedSurfacesRef.current.delete(
                `${toothNumber}_${surfaceName}`,
              );
            } else {
              // Add to selected surfaces
              selectedSurfacesRef.current.add(`${toothNumber}_${surfaceName}`);
              // Highlight surface
              box.traverse((surface) => {
                if (surface.isMesh && surface.userData.name === surfaceName) {
                  // Set highlight material - create a new one each time
                  //
                  // Add to tracked set
                  highlightedMeshesRef.current.add(surface);
                }
              });
            }
          } else {
            setSelectedSurfaces((prev) =>
              prev === `${toothNumber}_${surfaceName}`
                ? null
                : `${toothNumber}_${surfaceName}`,
            );
          }
          //  // Prevent context menu
          const modalWidth = 300;
          const modalHeight = 150;
          const padding = 35;

          const x = Math.min(
            window.innerWidth - modalWidth - padding,
            event.clientX,
          );
          const y = Math.min(
            window.innerHeight - modalHeight - padding,
            event.clientY,
          );
          openRightClickModal({ tooth: toothNumber, cursor: { x: x, y: y } });
          //
        }
      } else {
        setSelectedSurfaces(null);
      }
    },
    [getInteractiveObjects, gl, isInitialized, setSelectedSurfaces, camera],
  );

  // Keep the ref updated with the latest prop
  useEffect(() => {
    keyDownRef.current = keyDown;
    if (keyDown === "cntrl") {
      // Reset selected surfaces when Ctrl is pressed
      selectedSurfacesRef.current.clear();
      setSelectedSurfaces(null);
    } else {
      // Reset selected surfaces when Ctrl is released
      if (selectedSurfacesRef.current.size > 0) {
        // send the surafces
        setSelectedSurfaces(
          selectedSurfacesRef.current.size > 0
            ? Array.from(selectedSurfacesRef.current)
            : null,
        );

        // reset the selected surfaces
        setTimeout(() => {
          chartingBoxesRef.current.forEach((box) => {
            if (box) {
              box.traverse((surface) => {
                if (
                  surface.isMesh &&
                  surface.userData.originalMaterial &&
                  selectedSurfacesRef.current.has(
                    `${box.name.split("_")[0]}_${surface.userData.name}`,
                  )
                ) {
                  surface.material = surface.userData.originalMaterial.clone();
                  highlightedMeshesRef.current.delete(surface);
                }
              });
            }
          });

          selectedSurfacesRef.current.clear();
          setSelectedSurfaces(null);
        }, 200);
      }
    }
  }, [keyDown]);

  // Initialize when teeth are available
  useEffect(() => {
    if (chartingBoxesRef?.current?.size > 0 && !isInitialized) {
      setIsInitialized(true);
    }
  }, [chartingBoxesRef, isInitialized]);

  // Watch for changes in the teeth ref size with interval polling
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (chartingBoxesRef?.current?.size > 0 && !isInitialized) {
        setIsInitialized(true);
        clearInterval(intervalId);
      }
    }, 500);

    return () => {
      clearInterval(intervalId);
    };
  }, [chartingBoxesRef, isInitialized]);

  // Attach event listeners
  useEffect(() => {
    if (!isInitialized || !gl) return;

    // Validate that we have teeth refs before setting up event handlers
    if (!chartingBoxesRef || !chartingBoxesRef.current) {
      return;
    }

    const canvas = gl.domElement;
    canvas.style.touchAction = "none";

    // Use throttled versions of event handlers for better performance
    let lastMoveTime = 0;
    const throttleTime = 50; // Use original throttle time for better responsiveness

    const throttledMouseMove = (event) => {
      const now = Date.now();
      if (now - lastMoveTime > throttleTime) {
        lastMoveTime = now;
        handleMouseMove(event);
      }
    };

    canvas.addEventListener("mousemove", throttledMouseMove);
    canvas.addEventListener("mousedown", handleClick);
    canvas.addEventListener("contextmenu", (e) => e.preventDefault());

    return () => {
      canvas.removeEventListener("mousemove", throttledMouseMove);
      canvas.removeEventListener("click", handleClick);
      // Reset hover state
      handleHover(null);
    };
  }, [
    gl,
    chartingBoxesRef,
    isInitialized,
    handleClick,
    handleHover,
    handleMouseMove,
  ]);

  // Cleanup on unmount
  useEffect(() => {
    mountedRef.current = true;

    return () => {
      mountedRef.current = false;

      // Reset all highlighted materials
      resetAllHighlightedMeshes();

      // Reset hover state on unmount
      setHoveredTooth(null);
    };
  }, [resetAllHighlightedMeshes, setHoveredTooth]);

  return {
    isInitialized,
    resetAllHighlightedMeshes,
  };
};
