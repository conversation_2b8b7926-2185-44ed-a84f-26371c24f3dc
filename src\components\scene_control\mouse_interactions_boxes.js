import React from "react";
import { useThree } from "@react-three/fiber";
import { useMouseInteractionsBoxes } from "../../hooks/useMouseInteractionsBoxes";

export const MouseInteractionsBoxes = ({
  patientTeeth,
  setHoveredTooth,
  setSelectedTooth,
  setSelectedSurfaces,
  chartingBoxesRef,
  openRightClickModal,
  keyDown,
  selectedTreatment,
}) => {
  const { camera, gl } = useThree();
  // Use our custom hook for mouse interactions
  useMouseInteractionsBoxes({
    camera,
    gl,
    chartingBoxesRef,
    setHoveredTooth,
    setSelectedTooth,
    setSelectedSurfaces,
    openRightClickModal,
    keyDown,
    selectedTreatment,
  });

  return null;
};