# upod-v2 Technical Documentation

## 1. Executive Summary

- Brief overview of the project as a 3D dental visualization and treatment planning application.
- Key technologies used: React, Three.js, React Three Fiber.
- Main purpose: To provide dentists and dental professionals with an interactive tool for visualizing dental conditions, planning treatments, and communicating with patients.
- Target users: Dentists, orthodontists, dental students, and dental lab technicians.

## 2. Project Architecture Overview

### 2.1 Directory Structure

- `/src` - Main source code directory
  - `/apis` - API communication utilities (e.g., fetching patient data, saving treatment plans). Currently contains [`apis.js`](src/apis/apis.js) and [`post_messages.js`](src/apis/post_messages.js).
  - `/components` - React components, organized by function.
    - `/UI` - General user interface components (buttons, modals, toggles, etc.). Examples: [`ScreenshotButton.jsx`](src/components/UI/ScreenshotButton.jsx:1), [`RightClickModal.jsx`](src/components/UI/right_click_modal.jsx:1).
    - `/scene_control` - Components related to managing the 3D scene, such as camera controls and lighting. Examples: [`camera.js`](src/components/scene_control/camera.js:1), [`lights.js`](src/components/scene_control/lights.js:1), [`mouse_interactions.js`](src/components/scene_control/mouse_interactions.js:1).
    - `/views` - Components representing the main application views (Skull, Jaw, Charting, Single Treatment). Examples: [`skull.jsx`](src/components/views/skull.jsx:1), [`jaw.jsx`](src/components/views/jaw.jsx:1).
  - `/constants` - Configuration files and constant values (e.g., material definitions, camera settings, model paths). Examples: [`materials.js`](src/constants/materials.js:1), [`camera_config.js`](src/constants/camera_config.js:1).
  - `/context` - React Context providers for global state management. Example: [`TeethContext.jsx`](src/context/TeethContext.jsx:1).
  - `/helpers` - Helper functions for specific tasks or views. Examples: [`charting_helpers.js`](src/helpers/charting_helpers.js:1), [`skull_jaw_helpers.js`](src/helpers/skull_jaw_helpers.js:1).
  - `/hooks` - Custom React hooks for reusable logic. Examples: [`useModelLoader.js`](src/hooks/useModelLoader.js:1), [`useTeethMessages.js`](src/hooks/useTeethMessages.js:1).
  - `/utils` - General utility functions used across the application. Examples: [`modelUtils.js`](src/utils/modelUtils.js:1), [`treatmentUtils.js`](src/utils/treatmentUtils.js:1).
- `/public` - Static assets like `index.html`, icons, and potentially uncompiled 3D models if not served from S3.

### 2.2 Technology Stack

- **Frontend Framework**: React 19.0.0 (as per `package.json`)
- **3D Graphics**:
  - Three.js 0.161.0 (as per `package.json`)
  - React Three Fiber 9.0.4 (as per `package.json`)
  - Drei 10.0.0 (as per `package.json`)
- **State Management**: React Context API (implemented in [`src/context/TeethContext.jsx`](src/context/TeethContext.jsx:1))
- **Build Tool**: Vite 6.1.0 (as per `package.json` devDependencies)
- **Development Server**: Port 3001 (configurable in `vite.config.js`)
- **Model Format**: GLTF/GLB with DRACO compression (handled by `useModelLoader.js` and `useOptimizedModelLoader.js`)
- **Hosting**: AWS S3 for 3D models (URLs specified in [`src/constants/models.js`](src/constants/models.js:1))

## 3. Core Components and Architecture

### 3.1 Application Entry Points

- [`index.html`](index.html:1) - The main HTML entry point that loads the application.
- [`src/main.jsx`](src/main.jsx:1) - The JavaScript entry point where the React application is mounted to the DOM.
- [`src/AppWrapper.jsx`](src/AppWrapper.jsx:1) - A top-level wrapper component, likely responsible for providing global contexts, such as `TeethProvider`.
- [`src/App.jsx`](src/App.jsx:1) - The main application component that handles routing between different views and orchestrates overall application logic.

### 3.2 View Components

- **Skull View** ([`src/components/views/skull.jsx`](src/components/views/skull.jsx:1)) - Visualizes the full skull with teeth. Implements transparent rendering for the skull bone to allow visibility of teeth roots and underlying structures.
- **Jaw View** ([`src/components/views/jaw.jsx`](src/components/views/jaw.jsx:1)) - Focuses on either the upper (maxilla) or lower (mandible) jaw. Supports animations like jaw opening and closing.
- **Charting View** ([`src/components/views/charting.jsx`](src/components/views/charting.jsx:1)) - Provides a 2D-style dental charting interface, allowing users to select teeth and apply treatments in a more traditional dental chart format.
- **Single Treatment View** ([`src/components/views/single_treatment.jsx`](src/components/views/single_treatment.jsx:1)) - Displays a detailed view of a single tooth with applied treatments, often used for close-up inspection or planning specific procedures. (Note: The prompt mentioned `single_treatment.jsx`, the file seems to be `single_tooth.jsx` or `single_treatment_preview.jsx` based on file listing. Assuming `single_treatment.jsx` is the intended primary component for this view).

### 3.3 Component Hierarchy

```mermaid
graph TD
    A[App] --> AW[AppWrapper]
    AW --> V[ViewSwitcher]
    V --> SV[SkullView]
    V --> JV[JawView]
    V --> CV[ChartingView]
    V --> STV[SingleTreatmentView]

    SV --> SceneCanvas
    JV --> SceneCanvas
    CV --> SceneCanvas
    STV --> SceneCanvas

    SceneCanvas --> Base[Skull/Jaw/Charting Base Model]
    SceneCanvas --> TeethContainer[SkullJawTeeth / SingleTreatment]

    TeethContainer --> Tooth[Individual Tooth Model + Treatments]

    AW --> UIC[UI Components]
    UIC --> TL[TreatmentsList]
    UIC --> SB[ScreenshotButton]
    UIC --> AC[AnimationControls]
    UIC --> VS[ViewSwitcher]
```

## 4. 3D Rendering System

### 4.1 Model Loading System

- **Model Caching**: Implemented using `modelCache` and `animationCache` (likely within [`useOptimizedModelLoader.js`](src/hooks/useOptimizedModelLoader.js:1) or [`useModelLoader.js`](src/hooks/useModelLoader.js:1)) to prevent redundant fetching and processing of 3D models and animations.
- **GLTF Loader Configuration**: Uses `GLTFLoader` from Three.js, configured with `DRACOLoader` for compressed model support. This is crucial for handling detailed dental models efficiently.
- **Patient Type Handling**: Differentiates between "Adult" and "Children" models, loading appropriate assets based on the selected patient type (managed via `TeethContext`). Model paths are defined in [`src/constants/models.js`](src/constants/models.js:1).
- **Model Cloning**: Employs model cloning (`model.clone()`) for performance optimization, especially when multiple instances of the same tooth or treatment model are needed. This avoids reloading and re-parsing the same geometry.

### 4.2 Treatment Visualization Logic

**IMPORTANT FINDING**: Documented discrepancy between requested and implemented tooth visibility logic:

**Requested Logic**: "If treatments on a position have a root, don't add the healthy tooth; if treatments exist but none have a root, add the healthy tooth"

**Current Implementation**:

- The function [`treatmentHasRoots()`](src/utils/treatmentUtils.js:1) exists in [`src/utils/treatmentUtils.js`](src/utils/treatmentUtils.js:1) and is designed to check if any treatment in a list has associated root geometry.
- However, this function is **NOT currently used** in the primary logic that determines healthy tooth visibility.
- Healthy tooth visibility is primarily determined by the [`shouldShowHealthyTooth()`](src/utils/treatmentUtils.js:1) function (also in [`src/utils/treatmentUtils.js`](src/utils/treatmentUtils.js:1)). This function considers:
  - Whether there are active treatments on the tooth.
  - Indicators for missing teeth.
  - Planned extractions.
  - It does **NOT** currently incorporate a check for whether existing treatments have roots to decide if the healthy tooth model should be hidden.

**Gap**: The specific root-based visibility logic, as requested, is not implemented in the current codebase. The `treatmentHasRoots()` function is available but not integrated into the `shouldShowHealthyTooth()` decision-making process or equivalent logic.

### 4.4 Treatment Visibility Implementation Details

- When a tooth is loaded, `getModelParts()` determines which 3D models to load
- If no treatments exist, "Default" (healthy tooth) is included
- If treatments exist, specific treatment models are loaded instead
- The `shouldShowHealthyTooth()` function in treatmentUtils.js provides logic but is not integrated
- Treatment visibility can be toggled via the UI using `toggleTreatmentVisibility` in TeethContext

### 4.3 Materials and Rendering

- **Material Definitions**: Centralized material definitions are found in [`src/constants/materials.js`](src/constants/materials.js:1). This includes materials for healthy teeth, different types of treatments, gums, and bones.
- **Anti-Z-fighting**: Techniques like `polygonOffset`, `polygonOffsetFactor`, and `polygonOffsetUnits` are likely used on materials (as seen in [`src/constants/materials.js`](src/constants/materials.js:1)) to prevent Z-fighting artifacts when multiple treatment surfaces or models overlap.
- **Transparency Handling**: Transparency is used for skull/jaw views to see underlying teeth. This involves setting `transparent: true` and adjusting `opacity` on materials. Proper render order management (`renderOrder` property on objects) might also be in use, or depth write/test flags adjusted as needed, particularly handled by utilities in [`src/utils/transparencyUtils.js`](src/utils/transparencyUtils.js:1).

## 5. State Management

### 5.1 TeethContext

Global state management is primarily handled using React's Context API, specifically through `TeethContext` defined in [`src/context/TeethContext.jsx`](src/context/TeethContext.jsx:1). This context likely manages:

- **Patient Teeth Data**: The current state of all teeth, including their positions, existing conditions, and applied treatments. This data structure is fundamental for rendering and interaction.
- **Treatment Visibility Controls**: Flags or settings to toggle the visibility of different treatment types or categories.
- **Selection and Hover States**: Information about which tooth or surface is currently selected or hovered over by the user.
- **Patient Type Management**: The current patient type (e.g., "Adult", "Children") which affects model loading and potentially other UI aspects.
- Other global states like current view, animation states, etc.

### 5.2 Data Flow

```mermaid
sequenceDiagram
    participant User
    participant UI_Component
    participant InteractionHook
    participant TeethContext
    participant RenderingComponent
    participant ExternalEnv

    User->>UI_Component: Interacts (e.g., clicks tooth)
    UI_Component->>InteractionHook: Event detected
    InteractionHook->>TeethContext: Calls updater (e.g., setSelectedTooth)
    TeethContext->>TeethContext: Updates state
    TeethContext-->>RenderingComponent: State change propagates
    RenderingComponent->>RenderingComponent: Re-renders with new data

    ExternalEnv->>TeethContext: (via teethMessageHandler) Incoming message (e.g., initialize_teeth)
    TeethContext->>TeethContext: Updates state (e.g., setPatientTeeth)

    TeethContext-->>ExternalEnv: (via useTeethMessages & teethMessageHandler) Outgoing message (e.g., teeth_initialized)
```

## 6. Message Handling System

### 6.1 External Communication (iframe ↔ parent)

The application is designed to be embedded within an iframe and communicates with its parent window using the `postMessage` API.

- **Mechanism**: Uses `window.parent.postMessage` for sending messages and `window.addEventListener('message', ...)` for receiving them.
- **Message Types and Handlers**: Logic for handling incoming messages and dispatching outgoing messages is primarily located in [`src/utils/teethMessageHandler.js`](src/utils/teethMessageHandler.js:1) and potentially coordinated by the [`useTeethMessages.js`](src/hooks/useTeethMessages.js:1) hook.
- **Incoming Messages Examples**:
  - `initialize_teeth`: To load initial patient data.
  - `clear_teeth`: To reset the current dental state.
  - `set_patient_type`: To switch between adult and child models.
  - `apply_treatment`: To programmatically apply a treatment.
- **Outgoing Messages Examples**:
  - `tooth_rendered`: Confirmation that the 3D scene is ready.
  - `treatment_applied`: Notification that a user has applied a treatment.
  - `tooth_selected`: Information about the currently selected tooth.
  - `screenshot_taken`: Provides screenshot data.

### 6.2 Internal Events

The application may use a custom event system for decoupled communication between components, beyond standard React prop drilling or context.

- **Event Dispatchers and Listeners**: Utility functions for dispatching and listening to custom events are likely found in [`src/utils/messageUtils.js`](src/utils/messageUtils.js:1). This allows components to react to application-wide events without direct dependencies.
- **Use Cases**: Could be used for broadcasting events like "view_changed", "animation_started", or "model_loaded_globally".

## 7. Key Features

### 7.1 Mouse Interactions

- **Raycasting**: Uses Three.js `Raycaster` for 3D object selection. Logic is primarily in [`src/hooks/useMouseInteractions.js`](src/hooks/useMouseInteractions.js:1) (and its variants like [`useMouseInteractionsMD.js`](src/hooks/useMouseInteractionsMD.js:1) or [`useMouseInteractionsBoxes.js`](src/hooks/useMouseInteractionsBoxes.js:1)).
- **Hover and Click Handling**: Detects mouse hovers to highlight teeth/surfaces and clicks to select them or trigger actions (e.g., open treatment menu).
- **Surface Selection**: Allows selection of specific surfaces on a tooth model for precise treatment application.

### 7.2 Animation System

- **Jaw Opening/Closing Animations**: Supports animations for jaw movements, likely controlled via UI elements like sliders or buttons ([`src/components/UI/animation_controls.jsx`](src/components/UI/animation_controls.jsx:1)).
- **Animation Controls and Coordination**: The [`useAnimationControls.js`](src/hooks/useAnimationControls.js:1) hook likely manages animation state, playback, and timing. Animations are probably pre-baked into the GLTF models and controlled by manipulating animation actions in Three.js.
- **Frame-based Animation Timing**: Animations are likely driven by the `requestAnimationFrame` loop integrated with React Three Fiber's render loop.

### 7.3 Screenshot Functionality

- **Fixed-size Screenshot Capture**: Captures screenshots of the current 3D view, often at a predefined resolution for consistency. Logic is in [`src/utils/screenshotUtils.js`](src/utils/screenshotUtils.js:1) and UI in [`src/components/UI/ScreenshotButton.jsx`](src/components/UI/ScreenshotButton.jsx:1).
- **View-specific Camera Adjustments**: May temporarily adjust camera settings or scene elements to ensure optimal screenshot composition for different views.
- **S3 Upload Integration**: Screenshots can be uploaded to an S3 bucket. Message handling for this is in [`src/utils/screenshotMessageHandler.js`](src/utils/screenshotMessageHandler.js:1).

### 7.4 Treatment Management

- **Treatment Application Workflow**: Users can select teeth/surfaces and apply various predefined treatments. This involves updating the `TeethContext` and re-rendering the scene with new treatment models.
- **Multiple Treatment Support**: Allows multiple treatments to be applied to a single tooth, with logic to handle their visual layering and interaction.
- **Treatment Visibility Toggles**: UI controls (e.g., [`src/components/UI/TreatmentsList.jsx`](src/components/UI/TreatmentsList.jsx:1)) allow users to show/hide different categories or specific treatments.

## 8. Constants and Configuration

### 8.1 Model Paths

Defined in [`src/constants/models.js`](src/constants/models.js:1):

- **Adult models**: `https://upod.s3.eu-central-1.amazonaws.com/treatmentsV2/`
- **Children models**: `https://upod.s3.eu-central-1.amazonaws.com/treatmentsV3/child/`
  These base URLs are used by the model loading system to fetch appropriate GLTF/GLB files.

### 8.2 Camera Configuration

- **View-specific Camera Positions and Controls**: Camera settings (position, fov, near/far planes, target) are defined for each main view (Skull, Jaw, etc.) in [`src/constants/camera_config.js`](src/constants/camera_config.js:1).
- **Responsive Scaling**: The camera setup, possibly via [`src/utils/cameraUtils.js`](src/utils/cameraUtils.js:1), likely adapts to viewport size changes to maintain appropriate framing and aspect ratio. React Three Fiber's default camera handling also assists with this.

## 9. Development Guidelines

### 9.1 Adding New Treatments

1.  **Add Treatment Definition**: Update relevant dictionaries (likely in [`src/constants/dictionaries.js`](src/constants/dictionaries.js:1)) with the new treatment's metadata (name, ID, properties, associated model file names).
2.  **Create/Upload 3D Model**: Create the 3D model for the treatment in GLTF/GLB format (Draco compressed) and upload it to the appropriate S3 bucket path (under Adult or Children model paths).
3.  **Update `getModelParts()` Logic**: If the new treatment involves complex model part interactions or specific naming conventions not covered by generic logic, the [`getModelParts()`](src/utils/modelUtils.js:1) function in [`src/utils/modelUtils.js`](src/utils/modelUtils.js:1) might need adjustments.
4.  **Test Visibility and Rendering**: Thoroughly test the new treatment in all relevant views, checking for correct placement, material application, interaction with other treatments, and visibility logic.

### 9.2 Performance Considerations

- **Model Caching**: Aggressively use model caching ([`useOptimizedModelLoader.js`](src/hooks/useOptimizedModelLoader.js:1)) to avoid re-fetching and re-parsing models. This is critical for application responsiveness.
- **Use Cloned Models**: When multiple instances of a model are needed (e.g., same treatment on multiple teeth), clone the initially loaded model (`model.clone(true)`) instead of loading it multiple times.
- **Minimize Material Changes**: Changing material properties at runtime can be expensive. Define materials in [`src/constants/materials.js`](src/constants/materials.js:1) and reuse them. If dynamic changes are needed, explore efficient ways like using uniforms.
- **Optimize Raycasting**: Limit the frequency and scope of raycasting operations if performance issues arise during mouse interactions.
- **React Three Fiber Best Practices**: Follow performance guidelines for React Three Fiber, such as memoizing components and using instanced rendering where appropriate.

## 10. Known Issues and Future Improvements

### 10.1 Implementation Gaps

- **Root-based Tooth Visibility Logic**: As detailed in section 4.2, the requested logic to hide healthy teeth based on whether existing treatments have roots is not implemented. The [`treatmentHasRoots()`](src/utils/treatmentUtils.js:1) function exists but is not utilized for this purpose.
- **Empty API Files**: [`src/apis/apis.js`](src/apis/apis.js:1) and [`src/apis/post_messages.js`](src/apis/post_messages.js:1) appear to be placeholders or minimally implemented, suggesting that direct backend API integrations (beyond S3 for models) might be planned but not yet developed.

### 10.2 Suggested Improvements

- **Implement Requested Tooth Visibility Logic**: Integrate the [`treatmentHasRoots()`](src/utils/treatmentUtils.js:1) function into the [`shouldShowHealthyTooth()`](src/utils/treatmentUtils.js:1) logic or an equivalent decision point to accurately reflect the desired behavior.
- **Add TypeScript**: Migrate the codebase to TypeScript for improved type safety, better developer tooling, and reduced runtime errors.
- **Implement Proper Error Boundaries**: Add React error boundaries to gracefully handle rendering errors in different parts of the application and prevent a full crash.
- **Add Comprehensive Testing Suite**: Expand testing beyond existing unit tests (e.g., [`ToggleSwitch.test.jsx`](src/components/UI/ToggleSwitch.test.jsx:1), [`screenshotUtils.test.js`](src/utils/screenshotUtils.test.js:1)). Include integration tests for component interactions and end-to-end tests for user workflows.
- **Refactor Large Components**: Some components might grow large; consider breaking them down into smaller, more manageable pieces.
- **Code Documentation**: Improve inline code comments and JSDoc annotations for better maintainability.

## 11. Appendices

### 11.1 Treatment Types

- **Full Tooth Treatments**: Bridge, Apictomy, Externalsinuslift, RetainedRoot, BoneGraft, Crown, Implant, RootCanal, Extraction
- **Surface-based Treatments**: Filling, Decay, Sealant
- **Special Indicators**: MissingTooth (missing_tooth_indicator flag), MixedDentition (mixed_dentition flag)
- **Treatment Properties**:
  - full_tooth_treatment: boolean
  - patient_treatment: boolean
  - remove_tooth_when_completed: boolean
  - remove_treatment_when_completed: boolean
  - bridge_treatment: boolean
  - missing_tooth_indicator: boolean
  - mixed_dentition: boolean

### 11.2 Message Types Reference

**Incoming Messages (Parent → iframe):**

- `initialize_teeth`: Initialize teeth data with patient information
- `clear_teeth`: Clear all teeth data
- `select_treatment`: Select a treatment for application
- `select_position_and_surfaces`: Select tooth position and surfaces
- `remove_tooth`: Remove a specific tooth
- `tooth_rendered`: Notification that a tooth has been rendered
- `view_tooth_history`: Request to view tooth history
- `set_mixed_dentition`: Set mixed dentition for a tooth
- `treatment_removed`: Notification that a treatment was removed
- `take_screenshot`: Request to capture a screenshot
- `toggle_treatment_visibility`: Toggle visibility of a specific treatment

**Outgoing Messages (iframe → Parent):**

- `teeth_initialized`: Confirmation that teeth were initialized
- `teeth_cleared`: Confirmation that teeth were cleared
- `treatment_selected`: Confirmation of treatment selection
- `position_and_surfaces_selected`: Confirmation of position/surface selection
- `tooth_removed`: Confirmation that tooth was removed
- `tooth_rendered`: Notification that tooth rendering is complete
- `view_tooth_history_requested`: Request to view tooth history
- `mixed_dentition_set`: Confirmation of mixed dentition setting
- `treatment_removed_confirmed`: Confirmation of treatment removal
- `screenshot_taken`: Screenshot capture complete with URL
- `treatment_visibility_toggled`: Confirmation of visibility toggle

## 12. Transparency System for Overlapping Treatments

This section details the system implemented to handle the visualization of multiple, potentially overlapping, treatments on a single tooth. The primary goal is to ensure that all applied treatments are discernible, with newer treatments appearing more prominently and older treatments becoming progressively more transparent.

### 12.1 Overview

When multiple treatments are applied to the same tooth, they might occupy the same 3D space or overlap. To provide a clear visual representation, the system employs a transparency and layering strategy. Newer treatments are rendered as more opaque and visually "on top" of older treatments, which are made more transparent. This allows clinicians to understand the history and composition of treatments on a tooth.

### 12.2 Technical Implementation Details

The transparency system relies on a few core principles applied dynamically when tooth models and their associated treatments are loaded:

1.  **Treatment Sorting**:

    - Treatments associated with a tooth are sorted based on their `created_at` timestamp in descending order (newest first). This sorted order determines the rendering hierarchy and transparency levels.
    - This sorting occurs within the `loadToothModel` functions of components like [`src/components/views/single_treatment.jsx`](src/components/views/single_treatment.jsx:265-271) and [`src/components/skull_jaw_teeth.jsx`](src/components/skull_jaw_teeth.jsx:324-330).

2.  **Material Modification**:
    - For each treatment mesh, its material is cloned and modified. The modifications are primarily handled by the [`applyAntiZFightingProperties`](src/utils/materialUtils.js:10) function in [`src/utils/materialUtils.js`](src/utils/materialUtils.js:1).
    - The `index` from the sorted list of treatments is a key parameter passed to this function.

### 12.3 Key Functions and Their Purposes

- **`applyAntiZFightingProperties(material, index, isTransparent)`** (in [`src/utils/materialUtils.js`](src/utils/materialUtils.js:10)):

  - This is the core utility responsible for adjusting material properties for transparency and Z-fighting prevention.
  - **`isTransparent = true`**: This flag enables transparency for the material.
  - **Opacity**: Calculates opacity based on the `index`. The formula typically used is `Math.max(0.15, 0.8 - index * 0.2)`, making the newest treatment (index 0) have 0.8 opacity, the next (index 1) 0.6, and so on, with a minimum opacity of 0.15.
  - **`polygonOffset`**: Sets `material.polygonOffset = true` and adjusts `material.polygonOffsetFactor = index * 1.0` and `material.polygonOffsetUnits = index * 1.0` to help prevent Z-fighting between surfaces at similar depths.
  - **`renderOrder`**: Sets `material.renderOrder = -index`. This ensures that objects with lower indices (newer treatments) are rendered later, effectively appearing on top of objects with higher indices (older treatments).
  - **`depthWrite`**: For transparent materials (`isTransparent = true`), `material.depthWrite` is set to `false` for older treatments (`index > 0`). This allows newer treatments to be correctly rendered even if they are behind these older, transparent layers. For the newest treatment (`index = 0`), `depthWrite` remains `true`.
  - **`blending`**: Sets `material.blending = THREE.NormalBlending` for standard alpha blending.
  - **Color Tint (Optional)**: May apply a slight color tint (e.g., blueish) to older treatments (`index > 0`) to further help distinguish layers. The saturation of the tint can increase with the `index`.

- **`loadToothModel`** (in [`src/components/views/single_treatment.jsx`](src/components/views/single_treatment.jsx:212) and [`src/components/skull_jaw_teeth.jsx`](src/components/skull_jaw_teeth.jsx:282)):
  - These functions orchestrate the loading of tooth and treatment models.
  - After fetching and sorting treatments for a tooth, they iterate through the treatment parts.
  - If multiple treatments exist, they call `applyAntiZFightingProperties` for each treatment mesh, passing the appropriate `index` from the sorted list.
  - They also directly set `obj.renderOrder = -index` on the treatment mesh (`obj`).

### 12.4 How the System Handles Multiple Overlapping Treatments

1.  **Sorting by Creation Date**: Treatments are always sorted chronologically, with the most recent treatment considered the "top" layer.

    ```javascript
    // Example from src/components/views/single_treatment.jsx:265-271
    const sortedTreatments = toothData.treatments
      ? [...toothData.treatments].sort((a, b) => {
          const dateA = new Date(a.created_at || 0);
          const dateB = new Date(b.created_at || 0);
          return dateB - dateA; // Newest first
        })
      : [];
    ```

2.  **Iterative Opacity**: The opacity of each treatment layer is determined by its position (`index`) in the sorted list.

    ```javascript
    // Inside applyAntiZFightingProperties in src/utils/materialUtils.js:27
    modifiedMaterial.opacity = Math.max(0.15, 0.8 - index * 0.2);
    ```

3.  **Render Order Control**: Newer treatments are explicitly rendered after older ones.

    ```javascript
    // Inside applyAntiZFightingProperties in src/utils/materialUtils.js:20
    modifiedMaterial.renderOrder = -index;
    // Also set directly in components like src/components/views/single_treatment.jsx:433
    obj.renderOrder = -index;
    ```

4.  **Depth Buffer Management**: `depthWrite` is disabled for older transparent layers, preventing them from occluding newer layers that might be spatially behind them but should be visible through the transparency.

    ```javascript
    // Inside applyAntiZFightingProperties in src/utils/materialUtils.js:45-52
    if (index > 0) {
      modifiedMaterial.depthWrite = false;
    } else {
      modifiedMaterial.depthWrite = true;
    }
    ```

5.  **Z-Fighting Prevention**: `polygonOffset` slightly shifts polygons along the camera's view direction, helping to resolve coplanar surface rendering issues.

### 12.5 Configuration Options or Constants Used

The primary parameters controlling the transparency effect are generally hardcoded within [`src/utils/materialUtils.js`](src/utils/materialUtils.js:1):

- **Initial Opacity**: `0.8` for the newest treatment.
- **Opacity Decrement per Layer**: `0.2`.
- **Minimum Opacity**: `0.15`.
- **Polygon Offset Factor/Units**: Typically `index * 1.0`.
- **Color Tint HSL Values**: (e.g., `hue = 0.6` for blue, saturation `0.3 * index`) if tinting is active.

### 12.6 Code Examples

**1. Applying properties in `applyAntiZFightingProperties` (from [`src/utils/materialUtils.js`](src/utils/materialUtils.js:1)):**

```javascript
// src/utils/materialUtils.js:23-53 (simplified)
if (isTransparent) {
  modifiedMaterial.transparent = true;
  modifiedMaterial.opacity = Math.max(0.15, 0.8 - index * 0.2); // Opacity calculation
  modifiedMaterial.blending = THREE.NormalBlending;

  // Optional: Color tint for older treatments
  if (index > 0) {
    const hue = 0.6; // Blue
    const saturation = 0.3 * index;
    // ... apply tint ...
  }

  // Depth handling for transparency
  if (index > 0) {
    modifiedMaterial.depthWrite = false; // Older transparent layers don't write to depth buffer
  } else {
    modifiedMaterial.depthWrite = true; // Newest layer writes to depth buffer
  }
}
// Polygon offset and render order
modifiedMaterial.polygonOffset = true;
modifiedMaterial.polygonOffsetFactor = index * 1.0;
modifiedMaterial.polygonOffsetUnits = index * 1.0;
modifiedMaterial.renderOrder = -index;
```

**2. Usage in `loadToothModel` (conceptual example based on [`src/components/views/single_treatment.jsx`](src/components/views/single_treatment.jsx:1)):**

```javascript
// Simplified from src/components/views/single_treatment.jsx loadToothModel
// ...
const sortedTreatments = /* ... sort treatments ... */;
// ...
child.traverse(obj => {
    if (obj.isMesh) {
        // ...
        if (sortedTreatments.length > 1 && treatmentForPart) {
            const index = sortedTreatments.indexOf(treatmentForPart);

            obj.material = applyAntiZFightingProperties(
                obj.material,
                index,
                true // Make it transparent
            );
            obj.renderOrder = -index; // Ensure render order is set

            // ... visibility logic ...
        }
    }
});
// ...
```

### 12.7 Performance Considerations

- **Material Cloning**: Materials are cloned before modification (`material.clone()`) to avoid side effects on shared materials. This is standard practice.
- **Per-Mesh Application**: The logic is applied to each relevant mesh of a treatment part. For complex treatments with many meshes, this involves multiple material instantiations and modifications. However, given the typical number of treatments and meshes per tooth, this is generally manageable.
- The system does not currently employ advanced techniques like order-independent transparency, relying on the sorting and `renderOrder` for layering, which is common and effective for many scenarios.
