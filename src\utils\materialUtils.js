import * as THREE from "three";

/**
 * Applies transparency and anti-z-fighting properties to a material
 * @param {THREE.Material} material - The material to modify
 * @param {number} index - The index of the treatment (newer treatments have lower indices)
 * @param {boolean} isTransparent - Whether the material should be transparent
 * @returns {THREE.Material} - The modified material
 */
export const applyAntiZFightingProperties = (
  material,
  index = 0,
  isTransparent = false,
) => {

  // Clone the material to avoid modifying the original
  const modifiedMaterial = material.clone();

  // Enable polygon offset to prevent z-fighting
  modifiedMaterial.polygonOffset = true;
  modifiedMaterial.polygonOffsetFactor = index * 1.0;
  modifiedMaterial.polygonOffsetUnits = index * 1.0;

  // Set render order based on index (higher index = rendered first)
  modifiedMaterial.renderOrder = 10 - index; // Negative so newer treatments (lower index) render later

  // Handle transparency
  if (isTransparent) {
    modifiedMaterial.transparent = true;
    // Stronger transparency difference between treatments
    // Newest treatment (index 0) has 0.8 opacity, and each older treatment gets more transparent
    modifiedMaterial.opacity = Math.max(0.1, 0.7 - index * 0.2); // Steeper decrease in opacity for older treatments

    // Use a blending mode that works better for transparent overlapping objects
    modifiedMaterial.blending = THREE.NormalBlending;

    // Add a slight color tint to help distinguish between layers
    if (index > 0) {
      // Add a slight blue tint to older treatments
      const hue = 0.6; // Blue in HSL
      const saturation = 0.3 * index; // Increase saturation for older treatments
      const lightness = 0.5; // Keep lightness neutral

      // Convert HSL to RGB and apply as a slight tint
      const color = new THREE.Color().setHSL(hue, saturation, lightness);
      modifiedMaterial.color.lerp(color, 0.2); // Blend with original color
    }

    // For transparent materials, we need special depth handling
    // All transparent materials should have depthWrite = false for proper blending,
    // regardless of their index.
    modifiedMaterial.depthWrite = false;
    // Ensure alphaTest is at its default (0) to not interfere with opacity
    modifiedMaterial.alphaTest = 0;
    
  } else {
    
  }

  return modifiedMaterial;
};

/**
 * Creates a material for a treatment with anti-z-fighting properties
 * @param {Object} options - Material options
 * @param {number} options.index - The index of the treatment (newer treatments have lower indices)
 * @param {string} options.type - The type of treatment (e.g., 'Filling', 'Decay')
 * @param {boolean} options.isTransparent - Whether the material should be transparent
 * @returns {THREE.Material} - The created material
 */
export const createTreatmentMaterial = ({
  index = 0,
  type = "Default",
  isTransparent = false,
}) => {
  let baseMaterial;

  // Create appropriate base material based on treatment type
  switch (type) {
    case "Filling":
      baseMaterial = new THREE.MeshStandardMaterial({
        color: 0xffff00,
        emissive: 0x333300,
        metalness: 0.3,
        roughness: 0.4,
        side: THREE.DoubleSide,
      });
      break;
    case "Decay":
      baseMaterial = new THREE.MeshStandardMaterial({
        color: 0x000000,
        metalness: 0.1,
        roughness: 0.8,
        side: THREE.DoubleSide,
      });
      break;
    case "Crown":
      baseMaterial = new THREE.MeshStandardMaterial({
        color: 0xd3d3d3,
        metalness: 0.8,
        roughness: 0.2,
        side: THREE.DoubleSide,
      });
      break;
    default:
      baseMaterial = new THREE.MeshStandardMaterial({
        color: 0xffffff,
        metalness: 0.3,
        roughness: 0.4,
        side: THREE.DoubleSide,
      });
  }

  // Apply anti-z-fighting properties
  return applyAntiZFightingProperties(baseMaterial, index, isTransparent);
};
