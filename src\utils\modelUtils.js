import * as THREE from "three";
import { MathUtils } from "three";

// Cache for models and animations
export const modelCache = new Map();
export const animationCache = new Map();
export const materialCaches = {
  jaw: new Map(),
  skull: new Map(),
  charting: new Map(),
  single_treatment: new Map(), // Add cache for single_treatment view
};

// Create a Y-position only animation clip
export const createYPositionOnlyClip = (originalClip) => {
  if (!originalClip) return null;

  const clip = originalClip.clone();
  clip.tracks = clip.tracks.map((track) => {
    if (
      (track.name.endsWith(".position") || track.name.endsWith(".rotation")) &&
      track instanceof THREE.VectorKeyframeTrack
    ) {
      const newValues = Array.from(track.values);
      for (let i = 0; i < newValues.length; i += 3) {
        newValues[i] = 0;
        newValues[i + 2] = 0;
      }
      return new THREE.VectorKeyframeTrack(track.name, track.times, newValues);
    }
    return track;
  });
  return clip;
};

// List of treatments that have state-specific models (A for skull, B for jaw)
const stateSpecificTreatments = [
  "ZygomaticImplant",
  "SinusDrop",
  "MarylandBridge",
  "MarylandWing",
  "ImplantFullDenture",
  "FlexiDenture",
  "PartialDenture",
  "Clasp",
  "State",
];

// Check if a treatment has state-specific models
export const checkForStateSpecificModel = (treatmentName) => {
  return stateSpecificTreatments.some(
    (prefix) =>
      treatmentName === prefix || treatmentName.startsWith(`${prefix}_`),
  );
};

// Get model parts based on treatment names
import { DUAL_LOAD_TREATMENTS } from "../constants/models";

export const getModelParts = (toothData) => {
  // If no treatments array or it's empty, return default
  if (!toothData.treatments || toothData.treatments.length === 0)
    return ["Default"];

  // Get all unique treatment names
  const treatmentNames = [...new Set(toothData.treatments.map((t) => t.name))];

  // Initialize parts array
  let parts = [];
  let requiresDefaultLoad = false;

  // Check if any treatment requires dual loading
  for (const name of treatmentNames) {
    if (DUAL_LOAD_TREATMENTS.has(name)) {
      requiresDefaultLoad = true;
      break;
    }
  }

  // If dual loading is required, add "Default" as the first part
  if (requiresDefaultLoad) {
    parts.push("Default");
  }

  // Process each treatment name
  treatmentNames.forEach((treatmentName) => {
    // Special case for Filling which needs both Decay and Filling parts
    if (treatmentName === "Filling") {
      if (!parts.includes("Decay")) parts.push("Decay");
      if (!parts.includes("Filling")) parts.push("Filling");
    } else {
      // For state-specific treatments, remove any state suffix
      const basePartName =
        stateSpecificTreatments.find((prefix) =>
          treatmentName.startsWith(prefix),
        ) || treatmentName;

      // Add the treatment name if it's not already in the parts array
      // and it's not "Default" (if "Default" was already added for dual loading)
      if (!parts.includes(basePartName)) {
        parts.push(basePartName);
      }
    }
  });

  // If no parts were added (e.g., only "Default" from dual load but no other treatments)
  // or if parts is empty for some other reason, ensure "Default" is returned.
  if (
    parts.length === 0 ||
    (parts.length === 1 &&
      parts[0] === "Default" &&
      !requiresDefaultLoad &&
      treatmentNames.length === 0)
  ) {
    return ["Default"];
  }

  // If only "Default" is in parts due to dual load requirement, but there are actual treatments,
  // this means the treatments themselves might have been "Default" which is fine.
  // The main goal is to ensure "Default" is present if a DUAL_LOAD_TREATMENT is active.
  // And if no treatments, or only non-dual-load treatments, it behaves as before.

  return parts;
};

// Get the correct model path based on patient type
import { BASE_URL, TREATMENTS_VERSION } from "../config/api";
// DUAL_LOAD_TREATMENTS is already imported at the top

export const getModelPathForPatientType = (
  number,
  treatmentName,
  patientType = "ADULT",
) => {
  // If patient type is CHILDREN, use the child directory
  if (patientType === "CHILDREN") {
    return `${BASE_URL}/${TREATMENTS_VERSION}/child/${treatmentName}/${number}C.glb`;
  }

  // Otherwise use the default path
  return treatmentName === "Default"
    ? `${BASE_URL}/${TREATMENTS_VERSION}/${treatmentName}/${number}.glb`
    : `${BASE_URL}/${TREATMENTS_VERSION}/${treatmentName}/${number}_${treatmentName}.glb`;
};

// Clone a model with proper material handling
export const cloneModel = (source, currentView = "jaw") => {
  if (!source) return null;

  const clone = source.clone(true);
  const materialCache = materialCaches[currentView];

  // Make sure materialCache exists
  if (!materialCache) {
    
    return clone;
  }

  clone.traverse((obj) => {
    if (obj.isMesh) {
      obj.uuid = MathUtils.generateUUID();

      const originalMaterialId = obj.material.uuid;
      let clonedMaterial;

      if (materialCache.has(originalMaterialId)) {
        clonedMaterial = materialCache.get(originalMaterialId);
      } else {
        clonedMaterial = obj.material.clone();
        clonedMaterial.uuid = MathUtils.generateUUID();
        materialCache.set(originalMaterialId, clonedMaterial);
      }

      obj.material = clonedMaterial;
      obj.geometry = obj.geometry.clone();
      obj.geometry.uuid = MathUtils.generateUUID();
    }
  });
  return clone;
};

// Find exact morph target for a surface
export const findExactSurfaceMorphTarget = (
  morphTargetNames,
  surfaceName,
  part,
) => {
  // Convert to lowercase for easier comparison
  const lowerSurface = surfaceName.toLowerCase();

  // For basic surfaces like "Distal" and "Mesial", we need exact matching
  if (lowerSurface === "distal" || lowerSurface === "mesial") {
    // From the logs, we can see the exact pattern of the morph target names
    return morphTargetNames.find((name) => {
      const lowerName = name.toLowerCase();

      if (part === "Filling") {
        // For filling part, it should match pattern "Filling_3_Distal" or "Filling_3_Mesial"
        return (
          lowerName.includes(`filling`) &&
          lowerName.endsWith(`_${lowerSurface}`)
        );
      } else {
        // Decay part
        // For decay part, it should match pattern "Caries_3_Distal" or "Caries_3_Mesial"
        return (
          lowerName.includes(`caries`) && lowerName.endsWith(`_${lowerSurface}`)
        );
      }
    });
  }

  // For compound surfaces, the regular matching is fine
  return morphTargetNames.find((name) =>
    name.toLowerCase().includes(lowerSurface),
  );
};

// Create standard materials
export const createDefaultMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0xffffff,
    metalness: 0.3,
    roughness: 0.2,
    transparent: false,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    depthWrite: true,
  });

export const createHighlightMaterial = (originalMaterial = null) => {
  // Build debug props object, filtering out undefined values
  const debugProps = {};
  if (originalMaterial) {
    const props = [
      "transparent",
      "opacity",
      "renderOrder",
      "depthWrite",
      "depthTest",
      "blending",
      "polygonOffset",
    ];
    props.forEach((prop) => {
      if (originalMaterial[prop] !== undefined) {
        debugProps[prop] = originalMaterial[prop];
      }
    });
  }

  // Start with base highlight properties
  const highlightProps = {
    color: 0xffd700,
    metalness: 0.4,
    roughness: 0.2,
    emissive: 0xffe57f,
    emissiveIntensity: 0.6,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
  };

  // If we have an original material, preserve its transparency properties
  if (originalMaterial) {
    // Always copy these core properties
    highlightProps.transparent = originalMaterial.transparent;
    highlightProps.opacity = originalMaterial.opacity;
    highlightProps.depthWrite = originalMaterial.depthWrite;
    highlightProps.polygonOffset = originalMaterial.polygonOffset;
    highlightProps.polygonOffsetFactor = originalMaterial.polygonOffsetFactor;
    highlightProps.polygonOffsetUnits = originalMaterial.polygonOffsetUnits;

    // Only copy these properties if they are defined to avoid warnings
    if (originalMaterial.renderOrder !== undefined) {
      highlightProps.renderOrder = originalMaterial.renderOrder;
    }
    if (originalMaterial.blending !== undefined) {
      highlightProps.blending = originalMaterial.blending;
    }
    if (originalMaterial.depthTest !== undefined) {
      highlightProps.depthTest = originalMaterial.depthTest;
    }
    if (originalMaterial.alphaTest !== undefined) {
      highlightProps.alphaTest = originalMaterial.alphaTest;
    }
    // Preserve side property if it exists
    if (originalMaterial.side !== undefined) {
      highlightProps.side = originalMaterial.side;
    }
  } else {
    // Default non-transparent properties
    highlightProps.transparent = false;
    highlightProps.depthWrite = true;
  }

  // Filter out undefined values from highlightProps to prevent warnings
  const cleanedProps = {};
  Object.keys(highlightProps).forEach((key) => {
    if (highlightProps[key] !== undefined) {
      cleanedProps[key] = highlightProps[key];
    }
  });

  const material = new THREE.MeshStandardMaterial(cleanedProps);

  return material;
};

export const createSurfaceHighlightMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0x008080,
    metalness: 0.2,
    roughness: 0.8,
    transparent: true,
    opacity: 0.5,
    side: THREE.DoubleSide,
    depthWrite: true,
  });

export const createSkullMaterial = () =>
  new THREE.MeshStandardMaterial({
    transparent: true,
    opacity: 0.35,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    emissiveIntensity: 0.15,
    depthWrite: false, // Important for transparency
    depthTest: true, // Keep depth testing
    roughness: 0.4,
    metalness: 0.1,
  });

export const createGumMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0xffb39f, // Pink color for gums
    transparent: true,
    opacity: 0.35,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    emissiveIntensity: 0.15,
    depthWrite: false, // Important for transparency
    depthTest: true, // Keep depth testing
    roughness: 0.4,
    metalness: 0.1,
  });

// Force a scene update
export const forceSceneUpdate = (renderer, scene, camera) => {
  if (!renderer || !scene || !camera) {
    return;
  }

  // Dispose render lists to force a full rebuild
  renderer.renderLists.dispose();

  // Render the scene
  renderer.render(scene, camera);
};

// Clear all teeth from the scene
export const clearAllTeethFromScene = (scene) => {
  if (!scene) {
    return;
  }

  // Find all teeth objects in the scene
  const teethObjects = [];
  const teethMeshes = [];
  const decayGroups = [];

  // First pass: identify all objects that might be teeth
  scene.traverse((object) => {
    // Check for objects with tooth-related names or userData
    const name = object.name.toLowerCase();
    const isTooth =
      (object.userData &&
        (object.userData.type === "tooth" || object.userData.isTooth)) ||
      name.includes("tooth") ||
      name.includes("teeth") ||
      name.includes("molar") ||
      name.includes("incisor") ||
      name.includes("canine") ||
      name.includes("premolar") ||
      name.includes("filling");

    // Check specifically for decay groups
    const isDecayGroup = name.includes("decay") || name.includes("caries");

    if (isTooth) {
      // Only include direct tooth objects, not their children
      if (
        !object.parent ||
        !object.parent.userData ||
        (object.parent.userData.type !== "tooth" &&
          !object.parent.userData.isTooth)
      ) {
        teethObjects.push(object);
      }
    }

    // Collect decay groups separately
    if (isDecayGroup) {
      decayGroups.push(object);
    }

    // Also collect all meshes with morph targets (likely teeth)
    if (
      object.isMesh &&
      object.morphTargetInfluences &&
      object.morphTargetInfluences.length > 0
    ) {
      teethMeshes.push(object);
    }
  });

  if (
    teethObjects.length === 0 &&
    teethMeshes.length === 0 &&
    decayGroups.length === 0
  ) {
    return;
  }

  // Remove all teeth objects from the scene
  teethObjects.forEach((object) => {
    if (object.parent) {
      object.parent.remove(object);
    }

    // Dispose resources
    object.traverse((child) => {
      if (child.isMesh) {
        // Clear morph targets
        if (child.morphTargetInfluences) {
          child.morphTargetInfluences.fill(0);
        }

        // Dispose geometry
        if (child.geometry) {
          child.geometry.dispose();
        }

        // Dispose materials
        if (Array.isArray(child.material)) {
          child.material.forEach((m) => m?.dispose());
        } else if (child.material) {
          child.material.dispose();
        }

        // Ensure the mesh is not visible
        child.visible = false;
      }
    });

    // Clear any references
    object.clear();
  });

  // Handle decay groups
  decayGroups.forEach((group) => {
    if (group.parent) {
      group.parent.remove(group);
    }

    // Dispose resources
    group.traverse((child) => {
      if (child.isMesh) {
        // Clear morph targets
        if (child.morphTargetInfluences) {
          child.morphTargetInfluences.fill(0);
        }

        // Dispose geometry
        if (child.geometry) {
          child.geometry.dispose();
        }

        // Dispose materials
        if (Array.isArray(child.material)) {
          child.material.forEach((m) => m?.dispose());
        } else if (child.material) {
          child.material.dispose();
        }

        // Ensure the mesh is not visible
        child.visible = false;
      }
    });

    // Clear any references
    group.clear();
  });

  // Handle any remaining meshes with morph targets
  teethMeshes.forEach((mesh) => {
    if (!mesh.parent) return; // Skip if already removed

    // Reset morph targets
    if (mesh.morphTargetInfluences) {
      mesh.morphTargetInfluences.fill(0);
    }

    // Make invisible
    mesh.visible = false;
  });

  // Special handling for position objects with teeth
  const positionObjects = [];
  scene.traverse((object) => {
    if (object.name.includes("_Pos")) {
      // Check if this position object has any children
      let hasTeethChildren = false;
      object.traverse((child) => {
        if (
          child !== object &&
          (child.name.includes("tooth") || child.name.includes("Caries"))
        ) {
          hasTeethChildren = true;
        }
      });

      if (hasTeethChildren) {
        positionObjects.push(object);
      }
    }
  });

  // Remove all children from position objects
  positionObjects.forEach((posObj) => {
    while (posObj.children.length > 0) {
      const child = posObj.children[0];
      posObj.remove(child);

      // Dispose resources
      if (child.isMesh) {
        if (child.geometry) child.geometry.dispose();
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach((m) => m?.dispose());
          } else {
            child.material.dispose();
          }
        }
      }
    }
  });
};
