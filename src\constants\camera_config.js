export const CAMERA_CONFIG = {
  position: [0, 0, 0.5],
  fov: 45,
  near: 0.1,
  far: 1000,
};

export const GRID_CAMERA_CONFIG = {
  position: [0, 0, 0.5],
  fov: 10,
  near: 0.1,
  far: 1000,
};

export const JAW_CAMERA_CONFIG = {
  position: [0, 0, 0.3],
  fov: 45,
  near: 0.1,
  far: 1000,
};

export const CONTROLS_CONFIG = {
  target: [0, 0, 0],
  minDistance: 0.2,
  maxDistance: 1,
  minPolarAngle: Math.PI / 4,
  maxPolarAngle: Math.PI * 0.75,
  autoRotateSpeed: 1.5,
  dampingFactor: 0.05,
  enableDamping: true,
  enablePan: true,
  enableZoom: true,
  enableRotate: true,
};

export const JAW_CONTROLS_CONFIG = {
  ...CONTROLS_CONFIG,
  target: [0, 0, 0],  
  minDistance: 0.135,
  maxDistance: 0.4,
};

export const GRID_CONTROLS_CONFIG = {
  ...CONTROLS_CONFIG,
  target: [0, 0, 0],
  minDistance: 0.8,
  maxDistance: 0.8,
  enableRotate: true,
  enableZoom: false,
  enablePan: false,
};

export const SINGLE_TREATMENT_CAMERA_CONFIG = {
  position: [0, 0, 0.4],
  fov: 20,
  near: 0.1,
  far: 1000,
};

export const SINGLE_TREATMENT_CONTROLS_CONFIG = {
  ...CONTROLS_CONFIG,
  target: [0, 0, 0],
  minDistance: 0.4,
  maxDistance: 0.4,
  enableRotate: true,
  enableZoom: false,
  enablePan: false,
  rotateSpeed: 0.5,
  minPolarAngle: 0,
  maxPolarAngle: Math.PI,
};