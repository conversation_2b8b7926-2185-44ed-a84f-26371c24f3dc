import * as THREE from 'three';

/**
 * Creates a visual indicator to show the layer number of a treatment
 * @param {number} index - The index of the treatment (newer treatments have lower indices)
 * @param {THREE.Vector3} position - The position to place the indicator
 * @param {THREE.Object3D} parent - The parent object to attach the indicator to
 * @returns {THREE.Object3D} - The created indicator object
 */
export const createLayerIndicator = (index, position, parent) => {
  // Create a small sphere to indicate the layer
  const geometry = new THREE.SphereGeometry(0.05, 8, 8);
  
  // Create a material with a color based on the index
  // Use a color scheme that's easy to distinguish
  const colors = [
    0xff0000, // Red (newest)
    0x00ff00, // Green
    0x0000ff, // Blue
    0xffff00, // Yellow
    0xff00ff, // Magenta
    0x00ffff, // <PERSON>an
    0xff8000, // Orange
    0x8000ff  // Purple (oldest)
  ];
  
  const colorIndex = Math.min(index, colors.length - 1);
  const material = new THREE.MeshBasicMaterial({ color: colors[colorIndex] });
  
  // Create the mesh
  const indicator = new THREE.Mesh(geometry, material);
  
  // Position the indicator
  indicator.position.copy(position);
  
  // Offset the indicator based on the index to prevent overlap
  indicator.position.y += 0.1 * (index + 1);
  
  // Add the indicator to the parent
  if (parent) {
    parent.add(indicator);
  }
  
  // Add a text label showing the layer number
  const canvas = document.createElement('canvas');
  canvas.width = 64;
  canvas.height = 64;
  const context = canvas.getContext('2d');
  context.fillStyle = 'white';
  context.font = 'bold 48px Arial';
  context.textAlign = 'center';
  context.textBaseline = 'middle';
  context.fillText(index + 1, 32, 32);
  
  const texture = new THREE.CanvasTexture(canvas);
  const labelMaterial = new THREE.SpriteMaterial({ map: texture });
  const label = new THREE.Sprite(labelMaterial);
  label.scale.set(0.1, 0.1, 0.1);
  label.position.copy(indicator.position);
  label.position.y += 0.1;
  
  // Add the label to the parent
  if (parent) {
    parent.add(label);
  }
  
  return indicator;
};

/**
 * Creates a legend to explain the layer indicators
 * @param {number} numLayers - The number of layers to show in the legend
 * @param {THREE.Object3D} parent - The parent object to attach the legend to
 * @returns {THREE.Object3D} - The created legend object
 */
export const createLayerLegend = (numLayers, parent) => {
  // Create a container for the legend
  const legend = new THREE.Group();
  
  // Position the legend in the top-right corner
  legend.position.set(0.5, 0.5, 0);
  
  // Create a background for the legend
  const bgGeometry = new THREE.PlaneGeometry(0.3, 0.05 * numLayers + 0.1);
  const bgMaterial = new THREE.MeshBasicMaterial({ 
    color: 0x000000,
    transparent: true,
    opacity: 0.7
  });
  const background = new THREE.Mesh(bgGeometry, bgMaterial);
  legend.add(background);
  
  // Create an entry for each layer
  for (let i = 0; i < numLayers; i++) {
    // Create a small sphere to indicate the layer
    const geometry = new THREE.SphereGeometry(0.02, 8, 8);
    
    // Use the same color scheme as the indicators
    const colors = [
      0xff0000, // Red (newest)
      0x00ff00, // Green
      0x0000ff, // Blue
      0xffff00, // Yellow
      0xff00ff, // Magenta
      0x00ffff, // Cyan
      0xff8000, // Orange
      0x8000ff  // Purple (oldest)
    ];
    
    const colorIndex = Math.min(i, colors.length - 1);
    const material = new THREE.MeshBasicMaterial({ color: colors[colorIndex] });
    
    // Create the mesh
    const indicator = new THREE.Mesh(geometry, material);
    
    // Position the indicator in the legend
    indicator.position.set(-0.1, 0.05 * i - 0.05 * numLayers / 2 + 0.025, 0.01);
    
    // Add the indicator to the legend
    legend.add(indicator);
    
    // Add a text label
    const canvas = document.createElement('canvas');
    canvas.width = 128;
    canvas.height = 32;
    const context = canvas.getContext('2d');
    context.fillStyle = 'white';
    context.font = '24px Arial';
    context.textAlign = 'left';
    context.textBaseline = 'middle';
    context.fillText(`Layer ${i + 1}`, 10, 16);
    
    const texture = new THREE.CanvasTexture(canvas);
    const labelMaterial = new THREE.SpriteMaterial({ map: texture });
    const label = new THREE.Sprite(labelMaterial);
    label.scale.set(0.2, 0.05, 0.1);
    label.position.set(0.05, 0.05 * i - 0.05 * numLayers / 2 + 0.025, 0.01);
    
    // Add the label to the legend
    legend.add(label);
  }
  
  // Add the legend to the parent
  if (parent) {
    parent.add(legend);
  }
  
  return legend;
};
