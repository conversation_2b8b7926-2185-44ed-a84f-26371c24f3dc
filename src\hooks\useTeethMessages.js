import { useEffect, useCallback } from 'react';
import { useTeeth } from '../context/TeethContext';
import {
  initializeMessageListener,
  sendTeethInitialized,
  sendTeethCleared,
  sendPositionAndSurfacesSelected,
  sendChartedTreatmentRemoved,
  sendTreatmentRemoved,
  sendRemoveTooth,
  sendMixedDentition,
  sendBridgeTreatmentPositionsSelected,
  sendTreatmentSelected,
  sendAddAnnotations,
  sendViewToothHistory,
  sendToothRendered,
  sendChartedTreatmentCompleted,
  sendSingleToothInitialized,
  sendWatchTooth
} from '../utils/teethMessageHandler';

/**
 * Hook to handle teeth-related messages between UPOD and the iframe
 * @returns {Object} - Object containing message sending functions
 */
export const useTeethMessages = () => {
  const teethContext = useTeeth();

  // Initialize message listener
  useEffect(() => {
    const cleanup = initializeMessageListener(teethContext);

    return cleanup;
  }, [teethContext]);

  // Callback functions for sending messages

  const teethInitialized = useCallback(() => {
    return sendTeethInitialized();
  }, []);

  const teethCleared = useCallback(() => {
    return sendTeethCleared();
  }, []);

  const selectPositionAndSurfaces = useCallback((position, surfaces) => {
    return sendPositionAndSurfacesSelected(position, surfaces);
  }, []);

  const removeTreatment = useCallback((ctId) => {
    return sendChartedTreatmentRemoved(ctId);
  }, []);

  const treatmentRemoved = useCallback((position, ctId) => {
    return sendTreatmentRemoved(position, ctId);
  }, []);

  const removeTooth = useCallback((position) => {
    return sendRemoveTooth(position);
  }, []);

  const setMixedDentition = useCallback((active, position) => {
    return sendMixedDentition(active, position);
  }, []);

  const selectBridgeTreatmentPositions = useCallback((startPosition, endPosition) => {
    return sendBridgeTreatmentPositionsSelected(startPosition, endPosition);
  }, []);

  const selectTreatment = useCallback((treatment) => {
    return sendTreatmentSelected(treatment);
  }, []);

  const addAnnotations = useCallback((note, ctId) => {
    return sendAddAnnotations(note, ctId);
  }, []);

  const viewToothHistory = useCallback((position) => {
    return sendViewToothHistory(position);
  }, []);

  const toothRendered = useCallback(() => {
    return sendToothRendered();
  }, []);

  const completeTreatment = useCallback((ctId, position) => {
    return sendChartedTreatmentCompleted(ctId, position);
  }, []);

  const singleToothInitialized = useCallback(() => {
    return sendSingleToothInitialized();
  }, []);

  const watchTooth = useCallback((position, watched) => {
    return sendWatchTooth(position, watched);
  }, []);

  return {
    teethInitialized,
    teethCleared,
    selectPositionAndSurfaces,
    removeTreatment,
    treatmentRemoved,
    removeTooth,
    setMixedDentition,
    selectBridgeTreatmentPositions,
    selectTreatment,
    addAnnotations,
    viewToothHistory,
    toothRendered,
    completeTreatment,
    singleToothInitialized,
    watchTooth
  };
};

export default useTeethMessages;
