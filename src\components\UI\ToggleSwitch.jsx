import React from 'react';
import './ToggleSwitch.css';

/**
 * A toggle switch component for controlling visibility
 * @param {Object} props - Component props
 * @param {boolean} props.isVisible - Whether the item is visible
 * @param {Function} props.onToggle - Function to call when toggled
 * @param {string} props.treatmentId - ID of the treatment this toggle controls (for debugging)
 */
const ToggleSwitch = ({ isVisible, onToggle, treatmentId }) => {
  // Debug log in development mode
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    
  }
  return (
    <div className="visibility-control" data-treatment-id={treatmentId}>
      <span className="visibility-text">Visible</span>
      <label className="toggle-switch">
        <input
          type="checkbox"
          checked={isVisible}
          onChange={(e) => {
            // Debug log in development mode
            if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
              
            }
            onToggle(e);
          }}
        />
        <span className="toggle-slider"></span>
      </label>
    </div>
  );
};

export default ToggleSwitch;
