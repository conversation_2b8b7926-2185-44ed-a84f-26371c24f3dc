// Debug version of treatmentUtils.js with logging
// This file adds console logging to trace the root-based visibility logic issue

import { treatmentsData } from "../data/treatmentsData.js";

/**
 * Gets the ID of the treatment. Handles both 'id' and 'Id' property names.
 * @param {object} treatment - The treatment object.
 * @returns {string|number|null} The ID of the treatment, or null if not found.
 */
export const getTreatmentId = (treatment) => {
  if (!treatment) return null;
  return treatment.id ?? treatment.Id ?? null;
};

/**
 * Gets the Clinical Treatment ID (CTID) of the treatment.
 * @param {object} treatment - The treatment object.
 * @returns {string|number|null} The CTID of the treatment, or null if not found.
 */
export const getTreatmentCTID = (treatment) => {
  if (!treatment) return null;
  return treatment.ctid ?? null;
};

/**
 * Gets the name of the treatment.
 * @param {object} treatment - The treatment object.
 * @returns {string|null} The name of the treatment, or null if not found.
 */
export const getTreatmentName = (treatment) => {
  if (!treatment) return null;
  return treatment.name ?? null;
};

/**
 * Checks if the treatment is a full tooth treatment.
 * @param {object} treatment - The treatment object.
 * @returns {boolean} True if it's a full tooth treatment, false otherwise.
 */
export const isFullToothTreatment = (treatment) => {
  return !!(treatment && treatment.full_tooth_treatment);
};

/**
 * Checks if the treatment is marked as completed.
 * @param {object} treatment - The treatment object.
 * @returns {boolean} True if the treatment is completed, false otherwise.
 */
export const isTreatmentCompleted = (treatment) => {
  return !!(treatment && treatment.completed);
};

/**
 * Checks if the treatment is a patient treatment.
 * @param {object} treatment - The treatment object.
 * @returns {boolean} True if it's a patient treatment, false otherwise.
 */
export const isPatientTreatment = (treatment) => {
  return !!(treatment && treatment.patient_treatment);
};

/**
 * Checks if the tooth associated with this treatment should be removed when the treatment is completed.
 * @param {object} treatment - The treatment object.
 * @returns {boolean} True if the tooth should be removed upon completion, false otherwise.
 */
export const shouldRemoveToothWhenCompleted = (treatment) => {
  return !!(treatment && treatment.remove_tooth_when_completed);
};

/**
 * Checks if the treatment is a bridge treatment.
 * @param {object} treatment - The treatment object.
 * @returns {boolean} True if it's a bridge treatment, false otherwise.
 */
export const isBridgeTreatment = (treatment) => {
  return !!(treatment && treatment.bridge_treatment);
};

/**
 * Checks if the treatment is a missing tooth indicator.
 * @param {object} treatment - The treatment object.
 * @returns {boolean} True if it's a missing tooth indicator, false otherwise.
 */
export const isMissingToothIndicator = (treatment) => {
  return !!(treatment && treatment.missing_tooth_indicator);
};

/**
 * Checks if the treatment is related to mixed dentition.
 * @param {object} treatment - The treatment object.
 * @returns {boolean} True if it's a mixed dentition treatment, false otherwise.
 */
export const isMixedDentitionTreatment = (treatment) => {
  return !!(treatment && treatment.mixed_dentition);
};

/**
 * Checks if a treatment, identified by its name, involves tooth roots based on treatmentsData.
 * @param {string} treatmentName - The name of the treatment.
 * @returns {boolean} True if the treatment involves roots, false otherwise.
 */
export const treatmentHasRoots = (treatmentName) => {
  console.log("[DEBUG] treatmentHasRoots called with:", treatmentName);
  if (!treatmentName) return false;
  const treatmentDetails = treatmentsData.find(
    (t) => t.treatment_name === treatmentName,
  );
  const hasRoots = treatmentDetails ? treatmentDetails.root === "yes" : false;
  console.log(`[DEBUG] Treatment ${treatmentName} has roots: ${hasRoots}`);
  return hasRoots;
};

/**
 * Checks if a treatment, identified by its name, involves the tooth crown based on treatmentsData.
 * @param {string} treatmentName - The name of the treatment.
 * @returns {boolean} True if the treatment involves the crown, false otherwise.
 */
export const treatmentHasCrown = (treatmentName) => {
  if (!treatmentName) return false;
  const treatmentDetails = treatmentsData.find(
    (t) => t.treatment_name === treatmentName,
  );
  return treatmentDetails ? treatmentDetails.crown === "yes" : false;
};

/**
 * Determines the visibility of a treatment.
 * This is a placeholder and might need to be expanded based on specific application logic
 * (e.g., view modes, user settings to show/hide completed treatments).
 * @param {object} treatment - The treatment object.
 * @param {object} options - Optional parameters for visibility.
 * @param {boolean} [options.hideCompleted=false] - Whether to hide completed treatments.
 * @returns {boolean} True if the treatment should be visible, false otherwise.
 */
export const getTreatmentVisibility = (treatment, options = {}) => {
  if (!treatment) return false;

  const { hideCompleted = false } = options;

  if (hideCompleted && isTreatmentCompleted(treatment)) {
    return false;
  }

  // Add more complex visibility rules here if needed.
  // For example, specific handling for bridge components, missing tooth indicators in certain views, etc.

  return true; // Default to visible
};

/**
 * Sorts an array of treatments by a specified date field.
 * @param {object[]} treatments - An array of treatment objects.
 * @param {string} [dateField='created_at'] - The date field to sort by (e.g., 'created_at', 'completed_at').
 * @param {boolean} [ascending=true] - True for ascending order, false for descending.
 * @returns {object[]} A new array with sorted treatments.
 */
export const sortTreatmentsByDate = (
  treatments,
  dateField = "created_at",
  ascending = true,
) => {
  if (!Array.isArray(treatments)) return [];
  return [...treatments].sort((a, b) => {
    const dateA = new Date(a[dateField]);
    const dateB = new Date(b[dateField]);
    if (isNaN(dateA) || isNaN(dateB)) return 0; // Handle invalid dates gracefully

    return ascending ? dateA - dateB : dateB - dateA;
  });
};

/**
 * Filters an array of treatments based on a predicate function.
 * @param {object[]} treatments - An array of treatment objects.
 * @param {function} predicate - A function that takes a treatment and returns true if it should be included.
 * @returns {object[]} A new array with filtered treatments.
 */
export const filterTreatments = (treatments, predicate) => {
  if (!Array.isArray(treatments) || typeof predicate !== "function") return [];
  return treatments.filter(predicate);
};

/**
 * Filters treatments based on their completion status.
 * @param {object[]} treatments - An array of treatment objects.
 * @param {boolean} [completed=true] - True to get completed treatments, false for non-completed.
 * @returns {object[]} A new array of treatments filtered by completion status.
 */
export const filterCompletedTreatments = (treatments, completed = true) => {
  if (!Array.isArray(treatments)) return [];
  return treatments.filter((t) => isTreatmentCompleted(t) === completed);
};

/**
 * Filters treatments based on the patient_treatment flag.
 * @param {object[]} treatments - An array of treatment objects.
 * @param {boolean} [isPatient=true] - True to get patient treatments, false for non-patient treatments.
 * @returns {object[]} A new array of treatments filtered by the patient_treatment flag.
 */
export const filterPatientTreatments = (treatments, isPatient = true) => {
  if (!Array.isArray(treatments)) return [];
  return treatments.filter((t) => isPatientTreatment(t) === isPatient);
};

/**
 * Determines if a "healthy" tooth representation should be shown for a given tooth,
 * based on its treatments. This is a conceptual function and its logic will heavily
 * depend on how "healthy" is defined in the context of existing treatments.
 * For example, a tooth might be considered "not healthy" if it has any active,
 * non-cosmetic treatments.
 * @param {object[]} toothTreatments - An array of treatments associated with a specific tooth.
 * @returns {boolean} True if a healthy tooth model should be shown, false otherwise.
 */
export const shouldShowHealthyTooth = (toothTreatments) => {
  console.log("[DEBUG] shouldShowHealthyTooth called with:", toothTreatments);

  if (!Array.isArray(toothTreatments) || toothTreatments.length === 0) {
    console.log("[DEBUG] No treatments found, showing healthy tooth");
    return true; // No treatments, so show healthy tooth
  }

  // IMPORTANT: Root-based visibility logic is NOT implemented
  // The requested logic: "If treatments have a root, don't show healthy tooth"
  // Current implementation does NOT check for roots
  console.warn(
    "[DEBUG] Root-based visibility logic NOT IMPLEMENTED - treatmentHasRoots() function exists but is not used",
  );

  // Check if any treatments have roots (this is the missing implementation)
  const hasRootTreatments = toothTreatments.some((treatment) => {
    const hasRoot = treatmentHasRoots(getTreatmentName(treatment));
    console.log(
      `[DEBUG] Treatment ${getTreatmentName(treatment)} has root: ${hasRoot}`,
    );
    return hasRoot;
  });

  if (hasRootTreatments) {
    console.log(
      "[DEBUG] Found treatments with roots - should hide healthy tooth (NOT IMPLEMENTED)",
    );
    // TODO: Uncomment the line below to implement the requested logic
    // return false;
  }

  // Example logic: if there are any active (not completed) treatments that are not
  // purely cosmetic or preventative, then don't show the healthy tooth.
  // This needs to be tailored to the project's specific definition of "healthy".
  const hasSignificantActiveTreatment = toothTreatments.some(
    (treatment) =>
      !isTreatmentCompleted(treatment) &&
      !isMissingToothIndicator(treatment) && // Missing tooth indicators mean no healthy tooth
      // Add other conditions for treatments that don't obscure the "healthy" tooth,
      // e.g., "Whitening", "Sealant" if they are not considered "problem" indicators.
      // For now, any active treatment means the tooth isn't just "healthy".
      getTreatmentName(treatment) !== "Whitening" && // Example
      getTreatmentName(treatment) !== "Sealant", // Example
  );

  if (hasSignificantActiveTreatment) {
    console.log(
      "[DEBUG] Has significant active treatment, hiding healthy tooth",
    );
    return false;
  }

  // If a tooth has a "missing tooth indicator" treatment, it shouldn't be shown as healthy.
  if (toothTreatments.some(isMissingToothIndicator)) {
    console.log("[DEBUG] Has missing tooth indicator, hiding healthy tooth");
    return false;
  }

  // If a tooth is planned for extraction and it's not yet completed,
  // it might also influence showing the "healthy" base tooth.
  if (
    toothTreatments.some(
      (t) => shouldRemoveToothWhenCompleted(t) && !isTreatmentCompleted(t),
    )
  ) {
    console.log("[DEBUG] Tooth planned for extraction, hiding healthy tooth");
    return false;
  }

  console.log("[DEBUG] Showing healthy tooth by default");
  return true; // Default to showing healthy if no "significant" active treatments
};

/**
 * Groups treatments by tooth number.
 * Assumes treatments have a `tooth_number` property.
 * @param {object[]} treatments - An array of all treatment objects.
 * @returns {object} An object where keys are tooth numbers and values are arrays of treatments for that tooth.
 */
export const groupTreatmentsByTooth = (treatments) => {
  if (!Array.isArray(treatments)) return {};
  return treatments.reduce((acc, treatment) => {
    const toothNum = treatment.tooth_number; // Assuming 'tooth_number' property exists
    if (toothNum) {
      if (!acc[toothNum]) {
        acc[toothNum] = [];
      }
      acc[toothNum].push(treatment);
    }
    return acc;
  }, {});
};

/**
 * Checks if a treatment affects specific surfaces.
 * @param {object} treatment - The treatment object.
 * @param {string[]} surfaceKeys - An array of surface keys to check (e.g., ['M', 'O', 'D']).
 * @returns {boolean} True if the treatment has any of the specified surfaces, false otherwise.
 */
export const treatmentHasSurfaces = (treatment, surfaceKeys) => {
  if (
    !treatment ||
    !treatment.surfaces ||
    typeof treatment.surfaces !== "object" ||
    !Array.isArray(surfaceKeys)
  ) {
    return false;
  }
  return surfaceKeys.some(
    (key) =>
      Object.prototype.hasOwnProperty.call(treatment.surfaces, key) &&
      treatment.surfaces[key],
  );
};

/**
 * Determines the visibility of a specific part of a healthy tooth (root or crown)
 * based on the treatments applied to that tooth.
 *
 * Rules:
 * 1. A healthy part is generally hidden if any applied treatment to that tooth
 *    explicitly removes that part (e.g., treatment.root === 'no').
 * 2. Exception: A healthy part remains visible if any other treatment applied to
 *    the same tooth has `add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments`
 *    that matches the part ('root' or 'crown'), or is 'Any', or is 'Yes'.
 *
 * @param {object[]} toothTreatments - An array of treatment objects applied to the tooth.
 *                                     Each object should have a 'name' property.
 * @param {string} partName - The name of the tooth part to check ('root' or 'crown').
 * @returns {boolean} True if the healthy part should be visible, false otherwise.
 */
export const determineHealthyPartVisibility = (toothTreatments, partName) => {
  console.log(
    `[DEBUG] determineHealthyPartVisibility called for part: ${partName}, treatments:`,
    toothTreatments,
  );

  if (!toothTreatments || toothTreatments.length === 0) {
    console.log(`[DEBUG] No treatments, ${partName} is visible`);
    return true; // No treatments, so the healthy part is visible by default.
  }

  let partIsExplicitlyRemovedByAnyTreatment = false;
  let partIsExplicitlyKeptByAnyTreatment = false;

  for (const appliedTreatment of toothTreatments) {
    if (!appliedTreatment || !appliedTreatment.name) continue;

    const treatmentDetails = treatmentsData.find(
      (td) => td.treatment_name === appliedTreatment.name,
    );

    if (treatmentDetails) {
      // Check if this treatment explicitly removes the part
      if (treatmentDetails[partName] === "no") {
        console.log(
          `[DEBUG] Treatment ${appliedTreatment.name} removes ${partName}`,
        );
        partIsExplicitlyRemovedByAnyTreatment = true;
      }

      // Check if this treatment explicitly keeps the part (override)
      const addHealthyRule =
        treatmentDetails.add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments;
      if (
        addHealthyRule === partName ||
        addHealthyRule === "Any" ||
        addHealthyRule === "Yes" // Interpret 'Yes' as a generic keep/override instruction
      ) {
        console.log(
          `[DEBUG] Treatment ${appliedTreatment.name} keeps ${partName} (rule: ${addHealthyRule})`,
        );
        partIsExplicitlyKeptByAnyTreatment = true;
      }
    }
  }

  // If any treatment explicitly says to keep the part, it remains visible.
  if (partIsExplicitlyKeptByAnyTreatment) {
    console.log(`[DEBUG] ${partName} is kept visible by treatment rule`);
    return true;
  }

  // Otherwise, it's visible only if no treatment explicitly removes it.
  const isVisible = !partIsExplicitlyRemovedByAnyTreatment;
  console.log(`[DEBUG] ${partName} visibility: ${isVisible}`);
  return isVisible;
};

// Export all functions
export default {
  getTreatmentId,
  getTreatmentCTID,
  getTreatmentName,
  isFullToothTreatment,
  isTreatmentCompleted,
  isPatientTreatment,
  shouldRemoveToothWhenCompleted,
  isBridgeTreatment,
  isMissingToothIndicator,
  isMixedDentitionTreatment,
  treatmentHasRoots,
  treatmentHasCrown,
  getTreatmentVisibility,
  sortTreatmentsByDate,
  filterTreatments,
  filterCompletedTreatments,
  filterPatientTreatments,
  shouldShowHealthyTooth,
  groupTreatmentsByTooth,
  treatmentHasSurfaces,
  determineHealthyPartVisibility,
};
