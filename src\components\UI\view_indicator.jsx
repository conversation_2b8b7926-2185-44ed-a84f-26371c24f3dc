import React, { useState } from 'react';

export function ViewIndicator({ currentView }) {
  const [showGuide, setShowGuide] = useState(false);

  const toggleGuide = () => {
    setShowGuide(!showGuide);
  };

  return (
    <>
      <div className="view-indicator">
        <span>
          {currentView === "skull" && "Skull View"}
          {currentView === "jaw" && "Jaw View"}
          {currentView === "charting" && "Charting View"}
          {currentView === "single_treatment" && "Single Treatment View"}
        </span>
        <button
          className="info-button"
          onClick={toggleGuide}
          aria-label="Show navigation guide"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="16" x2="12" y2="12"></line>
            <line x1="12" y1="8" x2="12.01" y2="8"></line>
          </svg>
        </button>
      </div>

      {showGuide && (
        <div className="modal-overlay" onClick={toggleGuide}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>3D Navigation Guide</h3>
              <button className="close-button" onClick={toggleGuide}>×</button>
            </div>
            <div className="modal-body">
              <div className="guide-item">
                <div className="guide-icon">🔍</div>
                <div className="guide-text">
                  <strong>Zoom</strong>
                  <p>Use mouse wheel to zoom in and out</p>
                </div>
              </div>
              <div className="guide-item">
                <div className="guide-icon">🔄</div>
                <div className="guide-text">
                  <strong>Rotate</strong>
                  <p>Hold left mouse button and move</p>
                </div>
              </div>
              <div className="guide-item">
                <div className="guide-icon">↔️</div>
                <div className="guide-text">
                  <strong>Move/Pan</strong>
                  <p>Hold right mouse button and move</p>
                </div>
              </div>
              <div className="guide-item">
                <div className="guide-icon">🦷</div>
                <div className="guide-text">
                  <strong>View Tooth Info</strong>
                  <p>Hover and click on a tooth</p>
                </div>
              </div>
              <div className="guide-item">
                <div className="guide-icon">🔄</div>
                <div className="guide-text">
                  <strong>Switch View</strong>
                  <p>Press the switch button on the left to toggle between skull and jaw view</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <style>{`
        .view-indicator {
        width: 14%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f0f4f8;
          padding: 8px 16px;
          border-radius: 20px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          font-weight: 500;
          gap: 10px;
          position: relative;
          z-index: 1001;
        }

        .info-button {
          background: none;
          border: none;
          color: #4a5568;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background-color: #e2e8f0;
          transition: all 0.2s ease;
          position: relative;
          z-index: 1002;
        }

        .info-button:hover {
          background-color: #cbd5e0;
          color: #2d3748;
        }

        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal-content {
          background-color: white;
          border-radius: 12px;
          width: 90%;
          max-width: 500px;
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
          overflow: hidden;
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 24px;
          background-color: #f7fafc;
          border-bottom: 1px solid #e2e8f0;
        }

        .modal-header h3 {
          margin: 0;
          color: #2d3748;
          font-size: 1.25rem;
        }

        .close-button {
          background: none;
          border: none;
          font-size: 24px;
          color: #718096;
          cursor: pointer;
          padding: 4px 8px;
          border-radius: 4px;
          line-height: 1;
        }

        .close-button:hover {
          background-color: #edf2f7;
          color: #4a5568;
        }

        .modal-body {
          padding: 24px;
        }

        .guide-item {
          display: flex;
          margin-bottom: 20px;
          align-items: flex-start;
        }

        .guide-icon {
          font-size: 24px;
          margin-right: 16px;
          min-width: 30px;
          text-align: center;
        }

        .guide-text strong {
          display: block;
          margin-bottom: 4px;
          color: #2d3748;
        }

        .guide-text p {
          margin: 0;
          color: #4a5568;
        }
      `}</style>
    </>
  );
}

export default ViewIndicator;