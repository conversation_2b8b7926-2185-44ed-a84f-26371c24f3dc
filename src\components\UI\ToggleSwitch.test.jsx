import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import ToggleSwitch from './ToggleSwitch';

describe('ToggleSwitch Component', () => {
  test('renders correctly with default props', () => {
    const mockToggle = jest.fn();
    render(<ToggleSwitch isVisible={true} onToggle={mockToggle} />);
    
    // Check if the toggle switch is rendered
    const toggleSwitch = screen.getByRole('checkbox');
    expect(toggleSwitch).toBeInTheDocument();
    expect(toggleSwitch).toBeChecked();
  });

  test('calls onToggle when clicked', () => {
    const mockToggle = jest.fn();
    render(<ToggleSwitch isVisible={true} onToggle={mockToggle} />);
    
    // Click the toggle switch
    const toggleSwitch = screen.getByRole('checkbox');
    fireEvent.click(toggleSwitch);
    
    // Check if onToggle was called
    expect(mockToggle).toHaveBeenCalledTimes(1);
  });

  test('renders correctly when not visible', () => {
    const mockToggle = jest.fn();
    render(<ToggleSwitch isVisible={false} onToggle={mockToggle} />);
    
    // Check if the toggle switch is rendered but not checked
    const toggleSwitch = screen.getByRole('checkbox');
    expect(toggleSwitch).toBeInTheDocument();
    expect(toggleSwitch).not.toBeChecked();
  });
});
