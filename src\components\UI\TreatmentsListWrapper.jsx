import React, { useState } from 'react';
import ToggleSwitch from './ToggleSwitch';
import './TreatmentsList.css';

/**
 * Component to display a list of treatments for a tooth without requiring TeethContext
 * @param {Object} props - Component props
 * @param {Object} props.toothData - Data for the current tooth
 * @param {Function} props.onToggleVisibility - Function to call when visibility is toggled
 * @param {Function} props.getVisibility - Function to get visibility status
 */
const TreatmentsListWrapper = ({ toothData, onToggleVisibility, getVisibility }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  if (!toothData) {
    return <div className="treatments-list-container empty">No tooth data available</div>;
  }

  const { position, treatments = [], notes } = toothData;

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div className={`treatments-list-container ${isCollapsed ? 'collapsed' : ''}`}>
      <div className="treatments-list-header">
        <h3>Tooth {position}</h3>
        <div className="header-controls">
          <span className="treatments-count">{treatments.length} treatment(s)</span>
          <button className="collapse-button" onClick={toggleCollapse}>
            {isCollapsed ? '▼' : '▲'}
          </button>
        </div>
      </div>

      {!isCollapsed && (
        <div className="treatments-content">
          {notes && (
            <div className="tooth-notes">
              <h4>Notes:</h4>
              <p>{notes}</p>
            </div>
          )}

          {treatments.length === 0 ? (
            <div className="no-treatments">No treatments for this tooth</div>
          ) : (
            <>
              <h4 className="treatments-section-title">Treatments:</h4>
              <ul className="treatments-list">
                {treatments.map((treatment, index) => (
                  <li key={`treatment_${treatment.Id || treatment.id || index}_${index}`} className="treatment-item">
                    <div className="treatment-name">{treatment.name}</div>
                    <div className="treatment-details">
                      <span className="treatment-date">
                        {treatment.created_at ? new Date(treatment.created_at).toLocaleDateString() : 'No date'}
                      </span>
                      <span className={`treatment-status ${treatment.completed ? 'completed' : 'pending'}`}>
                        {treatment.completed ? 'Completed' : 'Pending'}
                      </span>
                    </div>
                    <ToggleSwitch
                      isVisible={getVisibility(toothData.position_number || toothData.number, `${treatment.Id || treatment.id || `treatment_${index}`}_${index}`)}
                      onToggle={() => {
                        // Log the toggle action for debugging
                        const baseId = treatment.Id || treatment.id || `treatment_${index}`;
                        const treatmentId = `${baseId}_${index}`;
                        
                        onToggleVisibility(toothData.position_number || toothData.number, treatmentId);
                      }}
                      treatmentId={`${treatment.Id || treatment.id || `treatment_${index}`}_${index}`} // Store the ID for debugging
                    />
                    {treatment.surfaces && Object.keys(treatment.surfaces).length > 0 && (
                      <div className="treatment-surfaces">
                        <span>Surfaces: </span>
                        {Object.keys(treatment.surfaces).join(', ')}
                      </div>
                    )}
                  </li>
                ))}
              </ul>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default TreatmentsListWrapper;
