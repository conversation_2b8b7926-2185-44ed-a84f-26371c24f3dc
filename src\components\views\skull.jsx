import { Component, useRef, useEffect } from "react";
import { useThree } from "@react-three/fiber";
import { useGLTF } from "@react-three/drei";
import { getModelUrl } from "../../constants/models"; // Import getModelUrl
import {
  createSkullMaterial,
  createGumMaterial,
} from "../../constants/materials";
import { useTeeth } from "../../context/TeethContext";

// Error Boundary Component
class ModelErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI.
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // You can also log the error to an error reporting service
    
    if (error && error.message && error.message.includes("403")) {
      
    }
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      // For now, returning null to not break the rest of the UI
      return null;
    }

    return this.props.children;
  }
}

function SkullInternal({ pointersRef }) {
  const skullRef = useRef(null);
  const { scene: threeScene } = useThree();
  const { getPatientType, getCurrentTreatmentName } = useTeeth(); // Assume getCurrentTreatmentName exists

  // Get the patient type (ADULT or CHILDREN)
  const patientType = getPatientType();
  // Get current treatment name, default to "Skull" for generic skull view
  const treatmentName = getCurrentTreatmentName
    ? getCurrentTreatmentName() || "Skull"
    : "Skull";

  const modelUrl = getModelUrl(treatmentName, "STATE_A", patientType);

  if (!modelUrl) {
    
    // Potentially return null or an error component if modelUrl is null
    // For now, this will cause useGLTF to fail, which is handled by ModelErrorBoundary
  }

  // Load the model with DRACO compression based on patient type
  const { scene } = useGLTF(modelUrl, {
    // Use the dynamic modelUrl
    draco: {
      decoderPath: "https://www.gstatic.com/draco/versioned/decoders/1.5.6/",
    },
  });

  // Helper function to recursively find and process pointers
  const findPointers = (object) => {
    if (object.name?.includes("_Pointer") && pointersRef?.current) {
      const number = parseInt(object.name.split("_")[0], 10);
      if (!isNaN(number)) {
        pointersRef.current.set(number, object);
      }
    }

    // Recursively process children
    if (object.children && object.children.length > 0) {
      object.children.forEach((child) => findPointers(child));
    }
  };

  useEffect(() => {
    if (scene) {
      try {
        // Create materials once
        const skullMaterial = createSkullMaterial();
        const gumMaterial = createGumMaterial();

        // Process materials
        scene.traverse((child) => {
          if (child.isMesh) {
            // Apply appropriate material based on mesh name
            if (
              child.name === "StateALowerGum" ||
              child.name === "StateAUpperGum" ||
              child.name === "ChildStateALowerGum" ||
              child.name === "ChildStateAUpperGum"
            ) {
              child.material = gumMaterial.clone();
            } else if (
              child.name === "Skeletal_Cranium" ||
              child.name === "Skeletal_Mandible" ||
              child.name === "ChildSkeletal_Cranium" ||
              child.name === "ChildSkeletal_Mandible"
            ) {
              child.material = skullMaterial.clone();
            }
          }
        });

        // Set initial position and rotation
        scene.position.set(0, 0, 0);
        scene.rotation.set(0, 0, 0);
        scene.scale.set(1, 1, 1);

        // Find all pointers in the scene hierarchy
        findPointers(scene);

        // Add to scene
        skullRef.current = scene;
        threeScene.add(scene);
      } catch (error) {
        
      }
    }

    return () => {
      if (skullRef.current) {
        threeScene.remove(skullRef.current);
      }
    };
  }, [scene, threeScene, pointersRef]);

  return null;
}

export function Skull({ pointersRef }) {
  return (
    <ModelErrorBoundary>
      <SkullInternal pointersRef={pointersRef} />
    </ModelErrorBoundary>
  );
}

// Don't preload the model - we'll load it only when needed

export default Skull;
