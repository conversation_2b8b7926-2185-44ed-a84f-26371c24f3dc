<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Screenshot Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    
    h1 {
      color: #333;
    }
    
    .container {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
    
    .iframe-container {
      width: 100%;
      height: 600px;
      border: 1px solid #ccc;
      border-radius: 4px;
      overflow: hidden;
    }
    
    iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
    
    .controls {
      display: flex;
      flex-direction: column;
      gap: 10px;
      padding: 15px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }
    
    button {
      padding: 10px 15px;
      background-color: #4a90e2;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;
    }
    
    button:hover {
      background-color: #3a80d2;
    }
    
    .result {
      margin-top: 20px;
      padding: 15px;
      background-color: #f0f0f0;
      border-radius: 4px;
      display: none;
    }
    
    .result.success {
      background-color: #e8f5e9;
      border-left: 4px solid #4caf50;
    }
    
    .result.error {
      background-color: #ffebee;
      border-left: 4px solid #f44336;
    }
    
    .result-image {
      max-width: 100%;
      margin-top: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <h1>Screenshot Test</h1>
  
  <div class="container">
    <div class="iframe-container">
      <iframe id="dental-iframe" src="/?view=charting" allowfullscreen></iframe>
    </div>
    
    <div class="controls">
      <h2>Controls</h2>
      <button id="take-screenshot">Take Screenshot</button>
      <div id="result" class="result">
        <h3>Result</h3>
        <p id="result-message"></p>
        <div id="result-image-container"></div>
      </div>
    </div>
  </div>
  
  <script>
    // Get elements
    const iframe = document.getElementById('dental-iframe');
    const takeScreenshotButton = document.getElementById('take-screenshot');
    const resultContainer = document.getElementById('result');
    const resultMessage = document.getElementById('result-message');
    const resultImageContainer = document.getElementById('result-image-container');
    
    // Listen for messages from the iframe
    window.addEventListener('message', (event) => {
      // Check if the message is a screenshot result
      if (event.data && event.data.type === 'screenshot_result') {
        // Show the result container
        resultContainer.style.display = 'block';
        
        // Set the result class based on success or failure
        if (event.data.success) {
          resultContainer.className = 'result success';
          resultMessage.textContent = `Screenshot uploaded successfully for patient ${event.data.patientId}`;
          
          // Display the image if a URL is provided
          if (event.data.url) {
            resultImageContainer.innerHTML = `
              <p>Screenshot URL: <a href="${event.data.url}" target="_blank">${event.data.url}</a></p>
              <img src="${event.data.url}" alt="Screenshot" class="result-image">
            `;
          } else {
            resultImageContainer.innerHTML = '';
          }
        } else {
          resultContainer.className = 'result error';
          resultMessage.textContent = `Error: ${event.data.message || 'Unknown error'}`;
          resultImageContainer.innerHTML = '';
        }
      }
    });
    
    // Add click handler for the take screenshot button
    takeScreenshotButton.addEventListener('click', () => {
      // Send a message to the iframe to take a screenshot
      iframe.contentWindow.postMessage({
        type: 'take_screenshot'
      }, '*');
      
      // Show loading state
      resultContainer.style.display = 'block';
      resultContainer.className = 'result';
      resultMessage.textContent = 'Taking screenshot...';
      resultImageContainer.innerHTML = '';
    });
  </script>
</body>
</html>
