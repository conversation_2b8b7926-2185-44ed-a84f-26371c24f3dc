import React, { useEffect, useRef, useImper<PERSON><PERSON><PERSON><PERSON> } from "react";
import { useThree } from "@react-three/fiber";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
import {
  CONTROLS_CONFIG,
  JAW_CONTROLS_CONFIG,
  GRID_CONTROLS_CONFIG,
  CAMERA_CONFIG,
  JAW_CAMERA_CONFIG,
  GRID_CAMERA_CONFIG,
  SINGLE_TREATMENT_CAMERA_CONFIG,
  SINGLE_TREATMENT_CONTROLS_CONFIG,
} from "../../constants/camera_config";

export const CameraAndControls = ({ viewMode, controlsRef: externalControlsRef }) => {
  const { camera, gl, size } = useThree();
  const internalControlsRef = useRef();
  const controlsRef = externalControlsRef || internalControlsRef;

  const scaleFactor = viewMode === 'single_treatment'
    ? 1.0
    : Math.min(size.width, size.height) / 1000;

  const getViewConfig = () => {
    const viewConfigs = {
      skull: {
        camera: CAMERA_CONFIG,
        controls: CONTROLS_CONFIG,
      },
      jaw: {
        camera: JAW_CAMERA_CONFIG,
        controls: JAW_CONTROLS_CONFIG,
      },
      charting: {
        camera: GRID_CAMERA_CONFIG,
        controls: GRID_CONTROLS_CONFIG,
      },
      single_treatment: {
        camera: SINGLE_TREATMENT_CAMERA_CONFIG,
        controls: SINGLE_TREATMENT_CONTROLS_CONFIG,
      }
    };

    const config = viewConfigs[viewMode] || viewConfigs.skull;
    return {
      camera: {
        ...config.camera,
      },
      controls: {
        ...config.controls,
        minDistance: config.controls.minDistance * scaleFactor,
        maxDistance: config.controls.maxDistance * scaleFactor
      }
    };
  };

  useEffect(() => {
    const controls = new OrbitControls(camera, gl.domElement);
    const config = getViewConfig();

    // Apply all control settings
    controls.target.set(...config.controls.target);
    controls.minDistance = config.controls.minDistance;
    controls.maxDistance = config.controls.maxDistance;
    controls.minPolarAngle =
      config.controls.minPolarAngle || CONTROLS_CONFIG.minPolarAngle;
    controls.maxPolarAngle =
      config.controls.maxPolarAngle || CONTROLS_CONFIG.maxPolarAngle;
    controls.autoRotateSpeed =
      config.controls.autoRotateSpeed || CONTROLS_CONFIG.autoRotateSpeed;
    controls.dampingFactor =
      config.controls.dampingFactor || CONTROLS_CONFIG.dampingFactor;
    controls.enableDamping =
      config.controls.enableDamping ?? CONTROLS_CONFIG.enableDamping;
    controls.enablePan = config.controls.enablePan ?? CONTROLS_CONFIG.enablePan;
    controls.enableZoom =
      config.controls.enableZoom ?? CONTROLS_CONFIG.enableZoom;
    controls.enableRotate =
      config.controls.enableRotate ?? CONTROLS_CONFIG.enableRotate;

    controlsRef.current = controls;

    // Expose controls for debugging in development mode
    if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
      window.__ORBIT_CONTROLS__ = controls;
    }

    // Update camera position and settings
    camera.position.set(...config.camera.position);
    camera.fov = config.camera.fov;

    // Update the camera
    camera.lookAt(...config.controls.target);
    camera.updateProjectionMatrix();
    controls.update();

    return () => controls.dispose();
  }, [camera, gl, viewMode, scaleFactor]);

  // Handle view changes
  useEffect(() => {
    if (controlsRef.current) {
      const config = getViewConfig();
      const controls = controlsRef.current;

      controls.target.set(...config.controls.target);
      controls.minDistance = config.controls.minDistance;
      controls.maxDistance = config.controls.maxDistance;
      controls.enableRotate =
        config.controls.enableRotate ?? CONTROLS_CONFIG.enableRotate;
      controls.enableZoom =
        config.controls.enableZoom ?? CONTROLS_CONFIG.enableZoom;
      controls.enablePan =
        config.controls.enablePan ?? CONTROLS_CONFIG.enablePan;

      if (config.controls.rotateSpeed !== undefined) {
        controls.rotateSpeed = config.controls.rotateSpeed;
      }

      if (config.controls.minPolarAngle !== undefined) {
        controls.minPolarAngle = config.controls.minPolarAngle;
      }

      if (config.controls.maxPolarAngle !== undefined) {
        controls.maxPolarAngle = config.controls.maxPolarAngle;
      }

      camera.position.set(...config.camera.position);
      camera.fov = config.camera.fov;
      camera.lookAt(...config.controls.target);
      camera.updateProjectionMatrix();

      controls.update();
    }
  }, [viewMode, camera, scaleFactor]);

  useEffect(() => {
    if (viewMode === 'single_treatment') {

      const handleResize = () => {
        if (controlsRef.current) {
          const config = getViewConfig();

          const aspect = size.width / size.height;

          let adjustedFov = config.camera.fov;
          if (aspect > 1) {
            adjustedFov = config.camera.fov / (aspect * 0.75);
          } else {
            adjustedFov = config.camera.fov * (aspect * 0.75);
          }

          adjustedFov = Math.max(10, Math.min(adjustedFov, 30));

          camera.fov = adjustedFov;
          camera.updateProjectionMatrix();

          controlsRef.current.update();

          // 
        }
      };

      handleResize();

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [viewMode, size, camera, getViewConfig]);

  // useImperativeHandle(cameraRef, () => ({
  //   resetCamera: () => {
  //     camera.position.set(0, 0, 0.5);
  //     camera.lookAt(0, 0, 0);
  //     controlsRef.current?.target.set(0, 0, 0);
  //     controlsRef.current?.update();
  //   },
  // }));

  return null;
};