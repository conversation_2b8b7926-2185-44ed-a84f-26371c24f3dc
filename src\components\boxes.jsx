import { useRef, useEffect, useState } from 'react';
import { Plane } from "@react-three/drei";
import { InteractiveSquare } from "./interactive_square.jsx";

export function Boxes({ pointersRef }) {
    const [pointers, setPointers] = useState([]);

    useEffect(() => {
        if (pointersRef?.current) {
            const updatedPointers = [];
            pointersRef.current.forEach((pointer) => {
                if (pointer) updatedPointers.push(pointer);
            });
            setPointers(updatedPointers);
        }
    }, [pointersRef.current]); // Reacts when pointersRef.current updates

    return (
        <>
            {pointers.map((pointer, index) => (
                <InteractiveSquare key={index} position={[pointer.position.x, pointer.position.y, pointer.position.z + 0.02]} />
            ))}
        </>
    );
}

export default Boxes;