# Code Optimization Documentation

This document outlines the optimizations and structural improvements made to the codebase.

## Optimizations Overview

1. **State Management**: Centralized patient teeth data management using Context API
2. **Component Structure**: Improved component organization and reusability
3. **Code Duplication**: Reduced duplicate code through custom hooks and utilities
4. **Performance Optimization**: Implemented memoization and reduced unnecessary re-renders
5. **Type Safety**: Added better type checking for props and state
6. **Error Handling**: Improved error handling and fallbacks

## New Structure

### Context API

The `TeethContext` provides centralized state management for teeth data:

```jsx
import { useTeeth } from './context/TeethContext';

// In your component
const { 
  patientTeeth, 
  addTreatment, 
  removeLastTreatment 
} = useTeeth();
```

### Custom Hooks

Several custom hooks have been created to encapsulate complex logic:

- `useAnimationControls`: Manages 3D model animations
- `useModelLoader`: Handles loading and caching 3D models
- `useMouseInteractions`: Manages mouse interactions with 3D teeth models

### Utility Functions

Common utilities have been extracted to separate files:

- `modelUtils.js`: Functions for 3D model handling
- `materialUtils.js`: Functions for creating and managing materials

## Implementation Guide

### Using the TeethContext

```jsx
// Add a treatment to a tooth
const { addTreatment } = useTeeth();
addTreatment(1, {
  id: "123",
  ctid: "456",
  name: "Filling",
  full_tooth_treatment: true,
  created_at: "2024-04-20",
  surfaces: {
    "DistalOcclusal": { "decaySeverity": 1, "fillingSize": 1 }
  }
});

// Remove the last treatment from a tooth
const { removeLastTreatment } = useTeeth();
removeLastTreatment(1);

// Mark a tooth as missing
const { markToothAsMissing } = useTeeth();
markToothAsMissing(1, true);
```

### Using the Animation Controls

```jsx
const { 
  playAnimations, 
  pauseAnimations, 
  resetAnimations 
} = useAnimationControls('teeth');

// Play animations
playAnimations();

// Pause animations
pauseAnimations();

// Reset animations
resetAnimations();
```

### Using the Model Loader

```jsx
const { loadModel, isLoading } = useModelLoader();

// Load a model
const loadTooth = async () => {
  const result = await loadModel('path/to/model.glb', 'jaw');
  if (result) {
    const { scene, animations } = result;
    // Use the loaded model
  }
};
```

## Migration Guide

To migrate existing components to use the new structure:

1. Replace direct state access with context:
   ```jsx
   // Before
   const [patientTeeth, setPatientTeeth] = useState({});
   
   // After
   const { patientTeeth, setPatientTeeth } = useTeeth();
   ```

2. Replace animation logic with the animation hook:
   ```jsx
   // Before
   const mixersRef = useRef(new Map());
   const playAnimations = () => { /* ... */ };
   
   // After
   const { mixersRef, playAnimations } = useAnimationControls();
   ```

3. Replace model loading logic with the model loader hook:
   ```jsx
   // Before
   const loaderRef = useRef(new GLTFLoader());
   const loadModel = async () => { /* ... */ };
   
   // After
   const { loadModel } = useModelLoader();
   ```

## Performance Benefits

- **Reduced Re-renders**: Context API prevents unnecessary re-renders
- **Optimized Model Loading**: Caching and efficient loading of 3D models
- **Memory Management**: Better cleanup of 3D resources
- **Code Size**: Smaller component files with focused responsibilities

## Compatibility Notes

The optimized code maintains compatibility with the existing charting view as requested. No changes were made to the charting-specific components to ensure they continue to function as before.
