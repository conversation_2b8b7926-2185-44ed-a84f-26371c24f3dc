import React, { useEffect, useRef, useState, lazy, Suspense } from "react";
import { Canvas } from "@react-three/fiber";
import { PerspectiveCamera, Environment, Loader } from "@react-three/drei";
import { CameraAndControls } from "./scene_control/camera";
import { MouseInteractions } from "./scene_control/mouse_interactions";
import { MouseInteractionsBoxes } from "./scene_control/mouse_interactions_boxes";
import { MouseInteractionsMD } from "./scene_control/mouse_interactions_MD";
import { ToothTag } from "./UI/tooth_tag";
import { Boxes } from "./boxes";
import ScreenshotButton from "./UI/ScreenshotButton";
import PatientIdInput from "./UI/PatientIdInput";
import {
  setScreenshotRenderer,
  initializeScreenshotMessageListener,
} from "../utils/screenshotMessageHandler";
import { RightClickModal } from "./UI/right_click_modal";

// Dynamically import components based on the current view
const Jaw = lazy(() => import("./views/jaw"));
const Skull = lazy(() => import("./views/skull"));
const Charting = lazy(() => import("./views/charting"));
const SingleTreatment = lazy(() => import("./views/single_treatment"));
const SkullJawTeeth = lazy(() => import("./skull_jaw_teeth"));
const ChartingTeeth = lazy(() => import("./charting_teeth"));

export default function Scene({
  currentView,
  chartingThirdRow,
  patientTeeth,
  setPatientTeeth,
  hoveredTooth,
  setHoveredTooth,
  selectedTooth,
  setSelectedTooth,
  hoveredMDTooth,
  setHoveredMDTooth,
  selectedMDTooth,
  setSelectedMDTooth,
  bridgeStart,
  setBridgeStart,
  selectedSurfaces,
  setSelectedSurfaces,
  selectedTreatment,
  eraserToolActive,
  skullPointersRef,
  jawPointersRef,
  chartingFVPointersRef,
  chartingTVPointersRef,
  chartingBVPointersRef,
  chartingWatchToolPointersRef,
  chartingFVChildPointersRef,
  chartingMDPointersRef,
  chartingMDChildPointersRef,
  isAnimationPlaying,
  rightClickData,
  openRightClickModal,
  closeRightClickModal,
  applyMissingTooth,
  applyResetTooth,
  missingToothActive,
  setMissingToothActive,
  resetTooth,
  setResetTooth,
  applyWatchTooth,
  watchTooth,
  setWatchTooth,
  applyMixedDentation,
  mixedDentation,
  setMixedDentation,
  keyDown,
}) {
  const containerRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const prevViewRef = useRef(currentView);
  const componentMountedRef = useRef(true);
  const rendererRef = useRef(null);
  const sceneRef = useRef(null);
  const cameraRef = useRef(null);
  const controlsRef = useRef(null);

  // Refs for teeth and morph controls
  const jawTeethRef = useRef(new Map());
  const skullTeethRef = useRef(new Map());
  const chartingFVTeethRef = useRef(new Map());
  const chartingTVTeethRef = useRef(new Map());
  const chartingBVTeethRef = useRef(new Map());
  const chartingBoxesRef = useRef(new Map());
  const teethMorphRef = useRef();

  // We don't need to manually apply morph targets anymore
  // The SkullJawTeeth component handles this internally based on the patientTeeth data
  useEffect(() => {
    // This effect is kept for future enhancements if needed
  }, [patientTeeth]);

  // Get current active refs based on view
  // Track active components by view to prevent memory leaks
  const [activeComponents, setActiveComponents] = useState({
    jaw: false,
    skull: false,
    charting: false,
    single_treatment: false,
  });

  // Get the current active teeth ref based on view
  const getCurrentTeethRef = () => {
    switch (currentView) {
      case "jaw":
        return jawTeethRef;
      case "skull":
        return skullTeethRef;
      case "charting":
        return chartingFVTeethRef;
      case "single_treatment":
        return null; // Single treatment handles its own refs
      default:
        return null;
    }
  };

  const getCurrentPointersRef = () => {
    switch (currentView) {
      case "jaw":
        return jawPointersRef;
      case "skull":
        return skullPointersRef;
      case "charting":
        return chartingFVPointersRef;
      case "single_treatment":
        return null; // Single treatment handles its own refs
      default:
        return null;
    }
  };

  // Clear previous view refs when changing views
  useEffect(() => {
    if (prevViewRef.current !== currentView) {
      // 

      // Update active components
      setActiveComponents((prev) => ({
        ...prev,
        [prevViewRef.current]: false,
        [currentView]: true,
      }));

      // Clear any hover state when changing views
      setHoveredTooth(null);
      setHoveredMDTooth(null);

      // Update the current view reference for screenshots
      if (rendererRef.current) {
        setScreenshotRenderer(
          rendererRef.current,
          cameraRef.current,
          controlsRef,
          currentView,
        );
      }

      prevViewRef.current = currentView;
    }
  }, [currentView, setHoveredTooth, setHoveredMDTooth]);

  // Debug the refs
  useEffect(() => {
    if (!componentMountedRef.current) return;

    // 
    // 

    // Log when hoveredTooth or selectedTooth changes
    // 
    // 
  }, [
    currentView,
    hoveredTooth,
    selectedTooth,
    jawTeethRef.current.size,
    skullTeethRef.current.size,
    chartingFVTeethRef.current.size,
  ]);

  // Sync animation state between components
  useEffect(() => {
    if (window.teethAnimationControls) {
      if (isAnimationPlaying && !window.teethAnimationControls.isPlaying()) {
        window.teethAnimationControls.play();
      } else if (
        !isAnimationPlaying &&
        window.teethAnimationControls.isPlaying()
      ) {
        window.teethAnimationControls.pause();
      }
    }
  }, [isAnimationPlaying]);

  // Handle window resize and loading
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        containerRef.current.style.height = `${window.innerHeight}px`;
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize();
    const timer = setTimeout(() => setIsLoading(false), 3000);

    return () => {
      window.removeEventListener("resize", handleResize);
      clearTimeout(timer);
    };
  }, []);

  // Component lifecycle
  useEffect(() => {
    componentMountedRef.current = true;
    // 

    // Initialize active components
    setActiveComponents({
      jaw: currentView === "jaw",
      skull: currentView === "skull",
      charting: currentView === "charting",
      single_treatment: currentView === "single_treatment",
    });

    return () => {
      componentMountedRef.current = false;
      // 
    };
  }, [currentView]);

  const viewProps = {
    patientTeeth,
    hoveredTooth,
    setHoveredTooth,
    selectedTooth,
    setSelectedTooth,
    hoveredMDTooth,
    setHoveredMDTooth,
    selectedMDTooth,
    setSelectedMDTooth,
    selectedTreatment,
    eraserToolActive,
  };

  const renderToothTags = () => {
    const currentTeethRef = getCurrentTeethRef();
    if (!currentTeethRef?.current) return null;

    return (
      <>
        {hoveredTooth && (
          <ToothTag
            target={currentTeethRef.current.get(hoveredTooth)}
            isHovered={true}
            isSelected={false}
          />
        )}
        {selectedTooth && (
          <ToothTag
            target={currentTeethRef.current.get(selectedTooth)}
            isHovered={false}
            isSelected={true}
          />
        )}
      </>
    );
  };

  // Only render one view at a time - this prevents material sharing between views
  const renderView = () => {
    return (
      <Suspense fallback={null}>
        {currentView === "jaw" && (
          <group key="jaw-view">
            <Jaw {...viewProps} pointersRef={jawPointersRef} />
            <SkullJawTeeth
              ref={teethMorphRef}
              patientTeeth={patientTeeth}
              pointersRef={jawPointersRef}
              currentView="jaw"
              teethRefStateA={jawTeethRef}
            />
            <MouseInteractions
              patientTeeth={patientTeeth}
              setHoveredTooth={setHoveredTooth}
              setSelectedTooth={setSelectedTooth}
              teethRef={jawTeethRef}
            />
            {renderToothTags()}
          </group>
        )}

        {currentView === "skull" && (
          <group key="skull-view">
            <Skull {...viewProps} pointersRef={skullPointersRef} />
            <SkullJawTeeth
              ref={teethMorphRef}
              patientTeeth={patientTeeth}
              pointersRef={skullPointersRef}
              currentView="skull"
              teethRefStateA={skullTeethRef}
            />
            <MouseInteractions
              patientTeeth={patientTeeth}
              setHoveredTooth={setHoveredTooth}
              setSelectedTooth={setSelectedTooth}
              teethRef={skullTeethRef}
            />
            {renderToothTags()}
          </group>
        )}

        {currentView === "charting" && (
          <group key="charting-view">
            <Charting
              patientTeeth={patientTeeth}
              setPatientTeeth={setPatientTeeth}
              pointersRef={{
                FV: chartingFVPointersRef,
                TV: chartingTVPointersRef,
                BV: chartingBVPointersRef,
              }}
              chartingBoxesRef={chartingBoxesRef}
              chartingThirdRow={chartingThirdRow}
              chartingWatchToolPointersRef={chartingWatchToolPointersRef}
              watchTooth={watchTooth}
              setWatchTooth={setWatchTooth}
              chartingFVChildPointersRef={chartingFVChildPointersRef}
              chartingMDPointersRef={chartingMDPointersRef}
              chartingMDChildPointersRef={chartingMDChildPointersRef}
              mixedDentation={mixedDentation}
              setMixedDentation={setMixedDentation}
            />
            <ChartingTeeth
              patientTeeth={patientTeeth}
              setPatientTeeth={setPatientTeeth}
              pointersRef={chartingFVPointersRef}
              currentView="charting"
              teethView="front"
              teethRef={chartingFVTeethRef}
              selectedTooth={selectedTooth}
              setSelectedTooth={setSelectedTooth}
              selectedMDTooth={selectedMDTooth}
              setSelectedMDTooth={setSelectedMDTooth}
              bridgeStart={bridgeStart}
              setBridgeStart={setBridgeStart}
              selectedSurfaces={selectedSurfaces}
              setSelectedSurfaces={setSelectedSurfaces}
              eraserToolActive={eraserToolActive}
              selectedTreatment={selectedTreatment}
              chartingThirdRow={chartingThirdRow}
              missingToothActive={missingToothActive}
              setMissingToothActive={setMissingToothActive}
              resetTooth={resetTooth}
              setResetTooth={setResetTooth}
              keyDown={keyDown}
              chartingMDChildPointersRef={chartingMDChildPointersRef}
            />
            <ChartingTeeth
              patientTeeth={patientTeeth}
              setPatientTeeth={setPatientTeeth}
              pointersRef={chartingTVPointersRef}
              currentView="charting"
              teethView="top"
              teethRef={chartingTVTeethRef}
              selectedTooth={selectedTooth}
              setSelectedTooth={setSelectedTooth}
              selectedMDTooth={selectedMDTooth}
              setSelectedMDTooth={setSelectedMDTooth}
              bridgeStart={bridgeStart}
              setBridgeStart={setBridgeStart}
              selectedSurfaces={selectedSurfaces}
              setSelectedSurfaces={setSelectedSurfaces}
              eraserToolActive={eraserToolActive}
              selectedTreatment={selectedTreatment}
              chartingThirdRow={chartingThirdRow}
              missingToothActive={missingToothActive}
              setMissingToothActive={setMissingToothActive}
              resetTooth={resetTooth}
              setResetTooth={setResetTooth}
              keyDown={keyDown}
              chartingMDChildPointersRef={chartingMDChildPointersRef}
            />
            <MouseInteractionsBoxes
              patientTeeth={patientTeeth}
              setHoveredTooth={setHoveredTooth}
              setSelectedTooth={setSelectedTooth}
              setSelectedSurfaces={setSelectedSurfaces}
              chartingBoxesRef={chartingBoxesRef}
              openRightClickModal={openRightClickModal}
              selectedTreatment={selectedTreatment}
              keyDown={keyDown}
            />
            <MouseInteractionsMD
              patientTeeth={patientTeeth}
              setHoveredMDTooth={setHoveredMDTooth}
              selectedMDTooth={selectedMDTooth}
              setSelectedMDTooth={setSelectedMDTooth}
              selectedTreatment={selectedTreatment}
              chartingMDChildPointersRef={chartingMDChildPointersRef}
              chartingMDPointersRef={chartingMDPointersRef}
            />
            {/* <ChartingTeeth
              patientTeeth={patientTeeth}
              setPatientTeeth={setPatientTeeth}
              pointersRef={chartingBVPointersRef}
              currentView="charting"
              teethView='back'
              teethRef={chartingBVTeethRef}
              selectedTooth={selectedTooth}
              setSelectedTooth={setSelectedTooth}
              selectedMDTooth={selectedMDTooth}
              setSelectedMDTooth={setSelectedMDTooth}
              bridgeStart={bridgeStart}
              setBridgeStart={setBridgeStart}
              selectedSurfaces={selectedSurfaces}
              setSelectedSurfaces={setSelectedSurfaces}
              eraserToolActive={eraserToolActive}
              selectedTreatment={selectedTreatment}
              chartingThirdRow={chartingThirdRow}
              missingToothActive={missingToothActive}
              setMissingToothActive={setMissingToothActive}
              resetTooth={resetTooth}
              setResetTooth={setResetTooth}
              keyDown={keyDown}
              chartingMDChildPointersRef={chartingMDChildPointersRef}
            /> */}
            {rightClickData && (
              <RightClickModal
                rightClickData={rightClickData}
                closeRightClickModal={closeRightClickModal}
                applyMissingTooth={applyMissingTooth}
                applyWatchTooth={applyWatchTooth}
                applyMixedDentation={applyMixedDentation}
                applyResetTooth={applyResetTooth}
              />
            )}
          </group>
        )}

        {currentView === "single_treatment" && (
          <group key="single-treatment-view">
            <SingleTreatment />
          </group>
        )}
      </Suspense>
    );
  };

  return (
    <>
      <div ref={containerRef} style={{ width: "100%", height: "100%" }}>
        <Canvas
          shadows
          style={{ width: "100%", height: "100%" }}
          onCreated={({ gl, scene, camera }) => {
            gl.setPixelRatio(Math.min(window.devicePixelRatio, 2));
            // Store references for screenshot functionality
            rendererRef.current = gl;
            sceneRef.current = scene;
            cameraRef.current = camera;

            // Attach scene and camera to renderer for screenshot
            gl.scene = scene;
            gl.camera = camera;

            // Enable preserveDrawingBuffer for screenshots
            gl.preserveDrawingBuffer = true;

            // Set the references for the screenshot message handler
            setScreenshotRenderer(gl, camera, controlsRef, currentView);

            // Log scene.sortObjects
            
            // Recommendation: Ensure scene.sortObjects is true for automatic transparency sorting by Three.js
            scene.sortObjects = true; // Enable automatic sorting for transparent objects
            
          }}
        >
          <PerspectiveCamera makeDefault position={[0, 0, 5]} fov={50} />
          <Environment preset="studio" background={false} blur={0.8} />
          <ambientLight intensity={0.5} />
          <spotLight
            position={[10, 10, 10]}
            angle={0.15}
            penumbra={1}
            castShadow
          />
          <pointLight position={[-10, -10, -10]} intensity={0.5} />
          <CameraAndControls viewMode={currentView} controlsRef={controlsRef} />
          {renderView()}
        </Canvas>
      </div>
      <Loader
        containerStyles={{ background: "rgba(255, 255, 255, 0.8)" }}
        dataStyles={{ color: "#000000", fontSize: "0.75rem" }}
        innerStyles={{ backgroundColor: "#f0f0f0" }}
        barStyles={{ backgroundColor: "#4a90e2" }}
        dataInterpolation={(p) => `Loading ${Math.floor(p)}%`}
        active={isLoading}
      />
      {/* Add screenshot button with all required props */}
      {rendererRef.current && cameraRef.current && controlsRef.current && (
        <ScreenshotButton
          renderer={rendererRef.current}
          camera={cameraRef.current}
          controls={controlsRef.current}
          currentView={currentView}
        />
      )}
    </>
  );
}
