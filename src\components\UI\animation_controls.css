.animation-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 10;
}

.animation-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  color: #333;
}

.animation-button:hover {
  background-color: #f8f8f8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.animation-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.animation-button.playing {
  background-color: #e5e7eb;
  color: #1f2937;
}

.animation-button svg {
  width: 16px;
  height: 16px;
}

@media screen and (max-width: 768px) {
  .animation-controls {
    bottom: 10px;
  }
  
  .animation-button {
    padding: 8px 16px;
    font-size: 12px;
  }
}