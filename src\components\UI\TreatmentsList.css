.treatments-list-container {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 300px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 15px;
  font-family: Arial, sans-serif;
  z-index: 10;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  transition: all 0.3s ease;
}

.treatments-list-container.collapsed {
  width: 200px;
  height: 60px;
  overflow: hidden;
}

.treatments-list-container.empty {
  padding: 20px;
  text-align: center;
  color: #666;
}

.treatments-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.collapse-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  padding: 2px 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.collapse-button:hover {
  background-color: #eee;
}

.treatments-section-title {
  margin: 15px 0 10px;
  font-size: 16px;
  color: #555;
}

.tooth-notes {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 15px;
}

.tooth-notes h4 {
  margin: 0 0 5px;
  font-size: 14px;
  color: #555;
}

.tooth-notes p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.treatments-list-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.treatments-count {
  font-size: 14px;
  color: #666;
  background-color: #f0f0f0;
  padding: 3px 8px;
  border-radius: 12px;
}

.no-treatments {
  text-align: center;
  padding: 20px 0;
  color: #666;
  font-style: italic;
}

.treatments-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.treatment-item {
  padding: 12px;
  margin-bottom: 10px;
  background-color: #f9f9f9;
  border-radius: 6px;
  border-left: 4px solid #4a90e2;
}

.treatment-name {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 5px;
  color: #333;
}

.treatment-details {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.treatment-date {
  color: #888;
}

.treatment-status {
  font-weight: bold;
}

.treatment-status.completed {
  color: #4caf50;
}

.treatment-status.pending {
  color: #ff9800;
}

.treatment-surfaces {
  font-size: 13px;
  color: #777;
  margin-top: 5px;
  padding-top: 5px;
  border-top: 1px dashed #eee;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .treatments-list-container {
    width: calc(100% - 40px);
    top: auto;
    bottom: 20px;
    right: 20px;
    max-height: 40vh;
  }
}
