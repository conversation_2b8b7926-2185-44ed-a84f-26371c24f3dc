import React from "react";
import { useThree } from "@react-three/fiber";
import { useMouseInteractions } from "../../hooks/useMouseInteractions";

export const MouseInteractions = ({
  patientTeeth,
  setHoveredTooth,
  setSelectedTooth,
  teethRef,
  openRightClickModal
}) => {
  const { camera, gl } = useThree();

  // Use our custom hook for mouse interactions
  useMouseInteractions({
    camera,
    gl,
    teethRef,
    setHoveredTooth,
    setSelectedTooth,
    openRightClickModal
  });

  return null;
};