/* Reset default margins and padding */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.app {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
}

/* UI Controls Container */
.ui-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* View switcher button */
.view-switcher {
  background-color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 160px;
}

.view-switcher:hover {
  background-color: #f8f8f8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.view-switcher:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.view-switcher svg {
  margin-right: 8px;
}

/* View indicator */
.view-indicator {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* App controls */
.app-controls {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  display: flex;
  gap: 15px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 90%;
  flex-wrap: wrap;
  justify-content: center;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  background-color: rgba(240, 240, 240, 0.7);
  padding: 10px;
  border-radius: 6px;
}

.button-group h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #333;
  text-align: center;
  border-bottom: 1px solid #ddd;
  padding-bottom: 5px;
}

.app-controls button {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.app-controls button:hover {
  background-color: #3a80d2;
}

.app-controls button:active {
  transform: scale(0.98);
}

.app-controls button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Media queries for different screen sizes */
@media screen and (max-width: 768px) {
  .app {
    /* height: calc(100vh - 60px); Account for mobile browser chrome */
  }

  .ui-controls {
    top: 10px;
    right: 10px;
  }

  .view-switcher {
    padding: 8px 16px;
    font-size: 12px;
    min-width: 120px;
  }

  .view-indicator {
    top: 10px;
    left: 10px;
    padding: 6px 12px;
    font-size: 12px;
  }

  .app-controls {
    top: 60px;
    padding: 8px;
    gap: 5px;
    flex-direction: column;
    align-items: center;
  }

  .button-group {
    min-width: unset;
    width: 100%;
  }

  .app-controls button {
    padding: 6px 10px;
    font-size: 12px;
  }
}

@media screen and (orientation: landscape) and (max-height: 500px) {
  .app {
    height: 100vh;
  }

  .ui-controls {
    flex-direction: row;
    top: 10px;
    right: 10px;
  }

  .app-controls {
    top: 10px;
    flex-direction: row;
  }
}