.patient-id-container {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 8px 16px;
  font-family: Arial, sans-serif;
  min-width: 200px;
  transition: all 0.3s ease;
}

.patient-id-display {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.patient-id-display:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.patient-id-label {
  font-weight: 600;
  font-size: 14px;
  color: #555;
}

.patient-id-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.patient-id-edit {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  color: #4a90e2;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 4px;
  margin-left: auto;
  transition: background-color 0.2s;
}

.patient-id-edit:hover {
  background-color: rgba(74, 144, 226, 0.1);
}

.patient-id-form {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.patient-id-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.patient-id-input:focus {
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.patient-id-buttons {
  display: flex;
  gap: 8px;
}

.patient-id-save,
.patient-id-cancel {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.patient-id-save {
  background-color: #4a90e2;
  color: white;
}

.patient-id-save:hover {
  background-color: #3a80d2;
}

.patient-id-cancel {
  background-color: #f0f0f0;
  color: #555;
}

.patient-id-cancel:hover {
  background-color: #e0e0e0;
}

@media screen and (max-width: 768px) {
  .patient-id-container {
    top: 10px;
    min-width: 180px;
    padding: 6px 12px;
  }
  
  .patient-id-label,
  .patient-id-value {
    font-size: 12px;
  }
  
  .patient-id-edit {
    font-size: 11px;
  }
  
  .patient-id-input {
    padding: 6px 10px;
    font-size: 12px;
  }
  
  .patient-id-save,
  .patient-id-cancel {
    padding: 4px 10px;
    font-size: 12px;
  }
}
