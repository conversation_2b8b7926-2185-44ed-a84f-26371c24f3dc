import React from 'react';
import './animation_controls.css';

export function AnimationControls({ onPlay, onReset, isPlaying }) {
  return (
    <div className="animation-controls">
      <button 
        className={`animation-button ${isPlaying ? 'active' : ''}`} 
        onClick={onPlay}
      >
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M5 4.99999L19 12L5 19V4.99999Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
        Play
      </button>
      
      <button className="animation-button" onClick={onReset}>
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z" stroke="currentColor" strokeWidth="2"/>
          <path d="M8 12L16 12M8 12L10.5 9.5M8 12L10.5 14.5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
        Reset
      </button>
    </div>
  );
}

export default AnimationControls;