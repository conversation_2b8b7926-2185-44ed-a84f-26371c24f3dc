import { useState, useRef, useEffect } from 'react';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { modelCache, animationCache, createYPositionOnlyClip, cloneModel } from '../utils/modelUtils';

export const useModelLoader = (resourcePath = './TreatmentsV4/') => {
  const loaderRef = useRef(new GLTFLoader());
  const [isLoading, setIsLoading] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [error, setError] = useState(null);
  const mountedRef = useRef(true);

  // Initialize the loader
  useEffect(() => {
    loaderRef.current.setResourcePath(resourcePath);
    loaderRef.current.setCrossOrigin('anonymous');

    return () => {
      mountedRef.current = false;
    };
  }, [resourcePath]);

  // Load a model with caching
  const loadModel = async (modelPath, currentView) => {
    if (!mountedRef.current) return null;
    
    setError(null);
    setIsLoading(true);
    setLoadingProgress(0);
    
    try {
      let gltf;
      let animations = [];
      
      // Check if model is in cache
      if (modelCache.has(modelPath)) {
        gltf = { 
          scene: cloneModel(modelCache.get(modelPath).scene, currentView) 
        };
        
        if (animationCache.has(modelPath)) {
          animations = animationCache.get(modelPath).map(anim => createYPositionOnlyClip(anim.clone()));
        }
        
        setLoadingProgress(100);
      } else {
        // Load model if not in cache
        gltf = await new Promise((resolve, reject) => {
          loaderRef.current.load(
            modelPath,
            resolve,
            (event) => {
              if (event.lengthComputable) {
                const progress = Math.round((event.loaded / event.total) * 100);
                setLoadingProgress(progress);
              }
            },
            reject
          );
        });
        
        // Cache the model
        modelCache.set(modelPath, { 
          scene: cloneModel(gltf.scene, currentView) 
        });
        
        if (gltf.animations?.length) {
          animationCache.set(
            modelPath, 
            gltf.animations.map(a => a.clone())
          );
          animations = gltf.animations.map(anim => createYPositionOnlyClip(anim));
        }
      }
      
      setIsLoading(false);
      return { scene: gltf.scene, animations };
    } catch (error) {
      
      setError(error);
      setIsLoading(false);
      return null;
    }
  };

  return {
    loadModel,
    isLoading,
    loadingProgress,
    error,
    loader: loaderRef.current
  };
};
