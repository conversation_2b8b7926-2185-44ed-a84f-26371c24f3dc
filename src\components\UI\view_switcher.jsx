
export function ViewSwitcher({ currentView, toggleView}) {
    // Don't show the view switcher for single_treatment view
    if (currentView === "single_treatment") {
        return null;
    }

    return (
        <div className="ui-controls">
            <button className="view-switcher" onClick={toggleView}>
            {currentView === "skull" ? (
                <>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M18.2218 11.7748C18.2948 11.9215 18.3333 12 18.3333 12C18.3333 12 18.2948 12.0785 18.2218 12.2252C18.0011 12.6754 17.6195 13.304 17.1147 13.9307C16.0919 15.1962 14.5258 16.6667 12.6667 16.6667C10.8075 16.6667 9.24142 15.1962 8.21858 13.9307C7.71382 13.304 7.33224 12.6754 7.11158 12.2252C7.03858 12.0785 7 12 7 12C7 12 7.03858 11.9215 7.11158 11.7748C7.33224 11.3246 7.71382 10.696 8.21858 10.0693C9.24142 8.80384 10.8075 7.33334 12.6667 7.33334C14.5258 7.33334 16.0919 8.80384 17.1147 10.0693C17.6195 10.696 18.0011 11.3246 18.2218 11.7748Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Switch to Jaw View
                </>
            ) : (
                <>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 16C15.866 16 19 12.866 19 9C19 5.13401 15.866 2 12 2C8.13401 2 5 5.13401 5 9C5 12.866 8.13401 16 12 16Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M12 16V22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M8 18H16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Switch to Skull View
                </>
            )}
            </button>
        </div>
    )
}

export default ViewSwitcher;