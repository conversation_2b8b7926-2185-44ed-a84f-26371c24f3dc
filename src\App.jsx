import { useState, useRef, useEffect } from "react";
import Scene from "./components/scene";
import ViewIndicator from "./components/UI/view_indicator";
import ViewSwitcher from "./components/UI/view_switcher";
import AnimationControls from "./components/UI/animation_controls";
// import TreatmentInput from "./components/UI/TreatmentInput"; // Added import
import { useTeeth } from "./context/TeethContext";
import { useTeethMessages } from "./hooks/useTeethMessages";
import { initializeScreenshotMessageListener } from "./utils/screenshotMessageHandler";
import "./App.css";

function App() {
  // View state
  const url = new URL(window.location.href);
  const viewParam = url.searchParams.get("view");
  const [currentView, setCurrentView] = useState(viewParam || "skull"); // Default to skull if no view param
  const [chartingThirdRow] = useState(false);
  const [isAnimationPlaying, setIsAnimationPlaying] = useState(false);
  const [rightClickData, setRightClickData] = useState(null);
  const [keyDown, setKeyDown] = useState(null);

  // Get teeth state from context
  const {
    patientTeeth,
    setPatientTeeth,
    hoveredTooth,
    setHoveredTooth,
    selectedTooth,
    setSelectedTooth,
    hoveredMDTooth,
    setHoveredMDTooth,
    selectedMDTooth,
    setSelectedMDTooth,
    bridgeStart,
    setBridgeStart,
    selectedSurfaces,
    setSelectedSurfaces,
    selectedTreatment,
    eraserToolActive,
    missingToothActive,
    setMissingToothActive,
    resetTooth,
    setResetTooth,
    watchTooth,
    setWatchTooth,
    mixedDentation,
    setMixedDentation,
    // setAllTeethTreatment - Uncomment if needed
  } = useTeeth();

  // Initialize teeth message handlers
  const teethMessages = useTeethMessages(); // Used for message handlers

  // Initialize screenshot message listener
  useEffect(() => {
    // Get the full teeth context
    const teethContext = {
      patientId: patientTeeth.patientId,
      patientTeeth,
    };

    // Initialize the screenshot message listener
    const cleanup = initializeScreenshotMessageListener(teethContext);

    return cleanup;
  }, [patientTeeth]);

  // No need to initialize teeth data here, it's handled by the AppWrapper

  // Pointer refs for skull and jaw
  const skullPointersRef = useRef(new Map());
  const jawPointersRef = useRef(new Map());
  const chartingFVPointersRef = useRef(new Map());
  const chartingTVPointersRef = useRef(new Map());
  const chartingBVPointersRef = useRef(new Map());
  const chartingWatchToolPointersRef = useRef(new Map());
  const chartingFVChildPointersRef = useRef(new Map());
  const chartingMDPointersRef = useRef(new Map());
  const chartingMDChildPointersRef = useRef(new Map());
  const cameraRef = useRef();

  // Toggle between skull and jaw views
  const toggleView = () => {
    setCurrentView(currentView === "skull" ? "jaw" : "skull");
  };

  const resetView = () => {
    cameraRef.current?.resetCamera();
  };

  const openRightClickModal = (data) => {
    setRightClickData(data);
  };

  const closeRightClickModal = () => {
    setRightClickData(null);
  };

  const applyMissingTooth = (tooth) => {
    if (tooth) {
      setMissingToothActive(tooth);
    }
  };

  const applyWatchTooth = (tooth) => {
    if (tooth) {
      setWatchTooth(tooth);
    }
  };

  const applyMixedDentation = (tooth) => {
    setMixedDentation(tooth);
  };

  const applyResetTooth = (tooth) => {
    if (tooth) {
      setResetTooth(tooth);

      setPatientTeeth((prevTeeth) => ({
        ...prevTeeth,
        [tooth]: {
          ...prevTeeth[tooth],
          status: "healthy",
          marked_as_missing: false,
          marked_as_watched: false,
          lastTreatment: null,
          notes: null,
          treatments: [],
        },
      }));
    }
  };

  // Handler for initializing test teeth data
  const handleInitializeTeethTest = () => {
    // Sample teeth data
    const sampleTeethData = {
      1: {
        position: "UL8",
        status: "filled",
        lastTreatment: "2024-02-20",
        notes: "Large filling placed",
        marked_as_missing: false,
        marked_as_watched: false,
        treatments: [
          {
            id: "111",
            ctid: "222",
            name: "Filling",
            full_tooth_treatment: false,
            need_default_tooth: false,
            created_at: "2024-02-20",
            surfaces: {
              DistalOcclusal: { decaySeverity: 1, fillingSize: 1 },
              Distal: { decaySeverity: 1, fillingSize: 1 },
              Mesial: { decaySeverity: 1, fillingSize: 1 },
            },
          },
        ],
      },
      2: {
        position: "UL7",
        status: "filled",
        lastTreatment: "2024-02-20",
        notes: "Large filling placed",
        marked_as_missing: false,
        marked_as_watched: false,
        treatments: [
          {
            id: "112",
            ctid: "223",
            name: "Filling",
            full_tooth_treatment: false,
            need_default_tooth: false,
            created_at: "2024-02-20",
            surfaces: {
              DistalOcclusal: { decaySeverity: 1, fillingSize: 1 },
              Distal: { decaySeverity: 1, fillingSize: 1 },
              Mesial: { decaySeverity: 1, fillingSize: 1 },
            },
          },
        ],
      },
      3: {
        position: "UL6",
        status: "filled",
        lastTreatment: "2024-02-20",
        notes: "Large filling placed",
        marked_as_missing: false,
        marked_as_watched: false,
        treatments: [
          {
            id: "113",
            ctid: "224",
            name: "Filling",
            full_tooth_treatment: false,
            need_default_tooth: false,
            created_at: "2024-02-20",
            surfaces: {
              DistalOcclusal: { decaySeverity: 1, fillingSize: 1 },
              Distal: { decaySeverity: 1, fillingSize: 1 },
              Mesial: { decaySeverity: 1, fillingSize: 1 },
            },
          },
        ],
      },
      4: {
        position: "UL5",
        status: "infected",
        lastTreatment: "2024-02-20",
        notes: "Infection detected",
        marked_as_missing: false,
        marked_as_watched: false,
        treatments: [
          {
            id: "114",
            ctid: "225",
            name: "BoneGraft",
            full_tooth_treatment: true,
            need_default_tooth: false,
            created_at: "2024-02-20",
          },
        ],
      },
    };

    // Simulate receiving a message from UPOD
    window.dispatchEvent(
      new MessageEvent("message", {
        data: {
          type: "initialize_teeth",
          teeth: Object.entries(sampleTeethData).map(([number, tooth]) => ({
            ...tooth,
            position_number: number,
          })),
        },
      }),
    );
  };

  // Handler for clearing teeth data
  const handleClearTeethTest = () => {
    // Simulate receiving a message from UPOD
    window.dispatchEvent(
      new MessageEvent("message", {
        data: {
          type: "clear_teeth",
        },
      }),
    );
  };

  // Updated animation control handlers to handle both jaw and teeth
  const handlePlayAnimation = () => {
    setIsAnimationPlaying(true);

    // Play both jaw and teeth animations
    if (window.jawAnimationControls) {
      window.jawAnimationControls.play();
    }

    if (window.teethAnimationControls) {
      window.teethAnimationControls.play();
    }
    // The teeth animations will be controlled through the jaw animation controls
    // that we've linked in the Teeth component
  };

  const handleResetAnimation = () => {
    // Reset both jaw and teeth animations
    setIsAnimationPlaying(false);

    if (window.jawAnimationControls) {
      window.jawAnimationControls.reset();
      setIsAnimationPlaying(false);
    }

    if (window.teethAnimationControls) {
      window.teethAnimationControls.reset();
    }
    // The teeth animations will be reset automatically through the linked controls
  };

  // Clear pointers when switching views to prevent conflicts
  useEffect(() => {
    if (currentView === "skull") {
      jawPointersRef.current.clear();
    } else if (currentView === "jaw") {
      skullPointersRef.current.clear();
    }
  }, [currentView]);

  // Update animation playing state based on jaw controls
  useEffect(() => {
    const checkAnimationPlaying = () => {
      if (window.jawAnimationControls) {
        setIsAnimationPlaying(window.jawAnimationControls.isPlaying());
      }
    };

    // Check initially and set up interval to check periodically
    checkAnimationPlaying();
    const interval = setInterval(checkAnimationPlaying, 200);

    return () => clearInterval(interval);
  }, []);

  // Cleanup animation controls on unmount
  useEffect(() => {
    return () => {
      if (window.jawAnimationControls) {
        window.jawAnimationControls.reset();
      }
      if (window.teethAnimationControls) {
        window.teethAnimationControls.reset();
      }
    };
  }, []);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "r" && !keyDown) {
        setKeyDown("r");
        //
      } else if (
        (event.key === "Control" || event.key === "Meta") &&
        !keyDown
      ) {
        setKeyDown("cntrl");
        //
      }
    };

    const handleKeyUp = (event) => {
      if (event.key === "r") {
        setKeyDown(null);
        setBridgeStart(null);
        //
      } else if (event.key === "Control" || event.key === "Meta") {
        setKeyDown(null);
        //
      }
    };

    // Add event listeners for keydown and keyup
    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);

    // Cleanup event listeners on unmount
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, [keyDown]);

  return (
    <div className="app">
      {(currentView === "skull" || currentView === "jaw") && (
        <div className="app-controls">
          <div className="button-group">
            {/* <h4>Data Management</h4>
          <button onClick={handleInitializeTeethTest}>Initialize Test Teeth</button>
          <button onClick={handleClearTeethTest}>Clear Teeth</button> */}
            {/* <button onClick={() => setAllTeethTreatment("BoneGraft")}>Apply BoneGraft to All</button> */}
          </div>
          {/* <div className="button-group">
          <h4>Treatment Actions</h4>
          <button onClick={() => teethMessages.selectTreatment({ id: 123, name: "Filling", full_tooth_treatment: false })}>Select Filling</button>
          <button onClick={() => teethMessages.selectPositionAndSurfaces("UL8", ["Mesial", "Distal"])}>Select UL8 Surfaces</button>
          <button onClick={() => selectedTooth && teethMessages.removeTooth(patientTeeth[selectedTooth]?.position)}>Remove Selected Tooth</button>
        </div>

        <div className="button-group">
          <h4>Other Actions</h4>
          <button onClick={() => teethMessages.toothRendered()}>Send Tooth Rendered</button>
          <button onClick={() => selectedTooth && teethMessages.viewToothHistory(patientTeeth[selectedTooth]?.position)}>View Tooth History</button>
          <button onClick={() => teethMessages.setMixedDentition(true, "UL5")}>Set UL5 Mixed Dentition</button>
          <button onClick={() => teethMessages.treatmentRemoved("UL8", "222")}>Remove UL8 Treatment</button>
        </div> */}
        </div>
      )}
      {(currentView === "skull" ||
        currentView === "jaw" ||
        currentView === "single_treatment") && (
        <>
          <ViewIndicator currentView={currentView} />
          <ViewSwitcher currentView={currentView} toggleView={toggleView} />
          {currentView === "jaw" && (
            <AnimationControls
              onPlay={handlePlayAnimation}
              onReset={handleResetAnimation}
              isPlaying={isAnimationPlaying}
            />
          )}
        </>
      )}
      {currentView === "charting" && (
        <div className="ui-controls">
          <button className="view-switcher" onClick={resetView}>
            Reset View
          </button>
        </div>
      )}
      <Scene
        currentView={currentView}
        cameraRef={cameraRef}
        chartingThirdRow={chartingThirdRow}
        patientTeeth={currentView === "single_treatment" ? {} : patientTeeth}
        setPatientTeeth={
          currentView === "single_treatment" ? () => {} : setPatientTeeth
        }
        hoveredTooth={currentView === "single_treatment" ? null : hoveredTooth}
        setHoveredTooth={
          currentView === "single_treatment" ? () => {} : setHoveredTooth
        }
        hoveredMDTooth={
          currentView === "single_treatment" ? null : hoveredMDTooth
        }
        setHoveredMDTooth={
          currentView === "single_treatment" ? () => {} : setHoveredMDTooth
        }
        selectedTooth={
          currentView === "single_treatment" ? null : selectedTooth
        }
        setSelectedTooth={
          currentView === "single_treatment" ? () => {} : setSelectedTooth
        }
        selectedMDTooth={
          currentView === "single_treatment" ? null : selectedMDTooth
        }
        setSelectedMDTooth={
          currentView === "single_treatment" ? () => {} : setSelectedMDTooth
        }
        bridgeStart={currentView === "single_treatment" ? null : bridgeStart}
        setBridgeStart={
          currentView === "single_treatment" ? () => {} : setBridgeStart
        }
        selectedSurfaces={
          currentView === "single_treatment" ? null : selectedSurfaces
        }
        setSelectedSurfaces={
          currentView === "single_treatment" ? () => {} : setSelectedSurfaces
        }
        selectedTreatment={
          currentView === "single_treatment" ? null : selectedTreatment
        }
        eraserToolActive={
          currentView === "single_treatment" ? false : eraserToolActive
        }
        skullPointersRef={
          currentView === "single_treatment" ? new Map() : skullPointersRef
        }
        jawPointersRef={
          currentView === "single_treatment" ? new Map() : jawPointersRef
        }
        chartingFVPointersRef={
          currentView === "single_treatment" ? new Map() : chartingFVPointersRef
        }
        chartingTVPointersRef={
          currentView === "single_treatment" ? new Map() : chartingTVPointersRef
        }
        chartingBVPointersRef={
          currentView === "single_treatment" ? new Map() : chartingBVPointersRef
        }
        chartingWatchToolPointersRef={
          currentView === "single_treatment"
            ? new Map()
            : chartingWatchToolPointersRef
        }
        chartingFVChildPointersRef={
          currentView === "single_treatment"
            ? new Map()
            : chartingFVChildPointersRef
        }
        chartingMDPointersRef={
          currentView === "single_treatment" ? new Map() : chartingMDPointersRef
        }
        chartingMDChildPointersRef={
          currentView === "single_treatment"
            ? new Map()
            : chartingMDChildPointersRef
        }
        isAnimationPlaying={isAnimationPlaying}
        rightClickData={rightClickData}
        openRightClickModal={openRightClickModal}
        closeRightClickModal={closeRightClickModal}
        applyMissingTooth={applyMissingTooth}
        missingToothActive={missingToothActive}
        setMissingToothActive={setMissingToothActive}
        applyResetTooth={applyResetTooth}
        resetTooth={resetTooth}
        setResetTooth={setResetTooth}
        applyWatchTooth={applyWatchTooth}
        watchTooth={watchTooth}
        setWatchTooth={setWatchTooth}
        applyMixedDentation={applyMixedDentation}
        mixedDentation={mixedDentation}
        setMixedDentation={setMixedDentation}
        keyDown={keyDown}
      />
    </div>
  );
}

export default App;
