import React from "react";
import { useThree } from "@react-three/fiber";
import { useMouseInteractionsMD } from "../../hooks/useMouseInteractionsMD";

export const MouseInteractionsMD = ({
  patientTeeth,
  setHoveredMDTooth,
  selectedMDTooth,
  setSelectedMDTooth,
  selectedTreatment,
  chartingMDChildPointersRef,
  chartingMDPointersRef,
}) => {
  const { camera, gl } = useThree();

  // Use our custom hook for mouse interactions
  useMouseInteractionsMD({
    camera,
    gl,
    patientTeeth,
    setHoveredMDTooth,
    selectedMDTooth,
    setSelectedMDTooth,
    selectedTreatment,
    chartingMDChildPointersRef,
    chartingMDPointersRef,
  });

  return null;
};