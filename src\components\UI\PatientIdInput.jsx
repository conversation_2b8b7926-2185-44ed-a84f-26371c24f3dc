import React, { useState } from 'react';
import { useTeeth } from '../../context/TeethContext';
import './PatientIdInput.css';

/**
 * Component for setting the patient ID
 */
const PatientIdInput = () => {
  const { patientId, setPatientIdentifier } = useTeeth();
  const [inputValue, setInputValue] = useState(patientId || '');
  const [isEditing, setIsEditing] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (inputValue.trim()) {
      setPatientIdentifier(inputValue.trim());
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setInputValue(patientId || '');
    setIsEditing(false);
  };

  return (
    <div className="patient-id-container">
      {isEditing ? (
        <form onSubmit={handleSubmit} className="patient-id-form">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Enter Patient ID"
            autoFocus
            className="patient-id-input"
          />
          <div className="patient-id-buttons">
            <button type="submit" className="patient-id-save">Save</button>
            <button type="button" onClick={handleCancel} className="patient-id-cancel">Cancel</button>
          </div>
        </form>
      ) : (
        <div className="patient-id-display" onClick={() => setIsEditing(true)}>
          <span className="patient-id-label">Patient ID:</span>
          <span className="patient-id-value">
            {patientId || 'Not set'}
          </span>
          <button className="patient-id-edit">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Edit
          </button>
        </div>
      )}
    </div>
  );
};

export default PatientIdInput;
