import React, { useRef, useEffect } from "react";
import { useThree, use<PERSON>rame } from "@react-three/fiber";
import { useGLTF } from "@react-three/drei";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import { DRACOLoader } from "three/examples/jsm/loaders/DRACOLoader";
import { MODELS } from "../../constants/models";
import * as THREE from "three";

export function Charting({ patientTeeth, setPatientTeeth, pointersRef, chartingBoxesRef, chartingThirdRow, chartingWatchToolPointersRef, watchTooth, setWatchTooth, chartingFVChildPointersRef, chartingMDPointersRef, chartingMDChildPointersRef, mixedDentation, setMixedDentation }) {
    const chartingRef = useRef(null);
    const loaderRef = useRef(new GLTFLoader());
    const { scene: threeScene } = useThree();

    // Load the model with DRACO compression
    const { scene, animations } = useGLTF(MODELS.CHARTING, {
        draco: {
        decoderPath: "https://www.gstatic.com/draco/versioned/decoders/1.5.6/",
        },
    });

    // Animate for third row
    // const mixer = new THREE.AnimationMixer(scene);
    // const action = mixer.clipAction(animations[0]);

    // Helper function to recursively find and process pointers
    const findPointers = (object, filter, exclude, pointers) => {
        if (object.name?.includes("_"+filter) && !object.name?.includes(exclude) && pointers?.current) {
        const number = parseInt(object.name.split("_")[0], 10);
            if (!isNaN(number)) {
                pointers.current.set(number, object);
            }
        }

        // Recursively process children
        object.children.forEach((child) => findPointers(child, filter, exclude, pointers));
    };

    useEffect(() => {
        if (scene) {
            // Set initial position and rotation
            scene.position.set(0, 0, 0);
            scene.rotation.set(0, 0, 0);
            scene.scale.set(1, 1, 1);

            // Update material of boxes
            const TVBoxPlaneMaterial = scene.getObjectByName('1_TVBox').children[0].material;
            TVBoxPlaneMaterial.transparent = true;
            TVBoxPlaneMaterial.opacity = 0;
            TVBoxPlaneMaterial.depthWrite = false;
            TVBoxPlaneMaterial.renderOrder = 1;
            const TVBoxMaterial = scene.getObjectByName('1_TVBox').material;
            TVBoxMaterial.roughness = 1;
            TVBoxMaterial.metalness = 0;
            TVBoxMaterial.envMapIntensity = 0;

            // Store boxes and hode them
            scene.traverse((obj) => {
                if (obj.name?.includes('_TVBox')) {
                    // add boxes to ref
                    const number = parseInt(obj.name.split("_")[0], 10);
                    chartingBoxesRef.current.set(parseInt(number, 10), obj);
                    obj.children.forEach(surface => {
                        const originalMaterial = surface.material.clone();
                        originalMaterial.transparent = true;
                        originalMaterial.opacity = 0;
                        originalMaterial.depthWrite = false;
                        originalMaterial.renderOrder = 1;
                        originalMaterial.roughness = 1;
                        originalMaterial.metalness = 0;
                        originalMaterial.side = THREE.FrontSide;
                        originalMaterial.depthWrite = true;
                        surface.material = originalMaterial;
                        surface.userData = {
                            number: number,
                            type: "surface",
                            name: surface.name.split("_")[1],
                            isInteractive: true,
                            originalMaterial: originalMaterial
                        };
                    });

                    // hide boxes
                    if (obj.isMesh) {
                        obj.visible = false;
                    }
                }
            });

            // Update material of gums
            const topGum = scene.getObjectByName('TopGum');
            const bottomGum = scene.getObjectByName('BottomGum');
            const gumMaterial = topGum.material;
            gumMaterial.transparent = true;
            gumMaterial.opacity = 0.5;
            gumMaterial.depthWrite = false;
            topGum.renderOrder = 1;
            bottomGum.renderOrder = 1;
        }
    }, [scene]);

    useEffect(() => {
        if (scene && !chartingRef.current) {
            chartingRef.current = scene;            

            // Animate for third row
            // if (chartingThirdRow){
            //     action.play(); // Start the animation (needed for updating time)
            //     action.time = animations[0].duration; // Move to last frame
            //     mixer.update(0); // Force update to apply the change
            //     action.paused = true; // Pause so it stays on the last frame
            // } else {
            //     action.play();
            //     action.time = 0;
            //     mixer.update(0);
            //     action.paused = true;
            // }

            // Find all pointers in the scene hierarchy
            findPointers(scene, "FV", "Child", pointersRef.FV);
            findPointers(scene, "TV", null, pointersRef.TV);
            findPointers(scene, "SV", null, pointersRef.BV); // named SV in the model
            findPointers(scene, "WatchToolPointer", null, chartingWatchToolPointersRef); // named SV in the model
            findPointers(scene, "FV_Child", null, chartingFVChildPointersRef);
            findPointers(scene, "MD_Child", null, chartingMDChildPointersRef);
            findPointers(scene, "MD", "Child", chartingMDPointersRef);

            // Add to scene
            if (!threeScene.children.includes(scene)) {
                threeScene.add(scene);
            }
        }

        // Cleanup
        return () => {
            if (chartingRef.current) {
            threeScene.remove(chartingRef.current);
            chartingRef.current = null;
            }
        };
    }, [scene, threeScene, pointersRef, chartingThirdRow, chartingWatchToolPointersRef]);

    // Pre-load DRACO decoder
    useEffect(() => {
        const dracoLoader = new DRACOLoader();
        dracoLoader.setDecoderPath(
        "https://www.gstatic.com/draco/versioned/decoders/1.5.6/",
        );

        return () => {
        dracoLoader.dispose();
        };
    }, []);

    const loadEyesModel = async (number, pointer) => {
        const gltf = await new Promise((resolve, reject) => {
        loaderRef.current.load(MODELS.WATCHTOOL, resolve, undefined, reject);
        })

        const model = gltf.scene.children[0];
        model.position.set(0, 0, 0);
        model.rotation.set(0, 0, 0);
        model.name = `eyes_${number}`;

        pointer.add(model);

        chartingWatchToolPointersRef.current.set(parseInt(number, 10), model);
    };

    const loadChildTooth = async (number, pointer) => {
        const gltf = await new Promise((resolve, reject) => {
        loaderRef.current.load(`./TreatmentsV4/Child/ChildHealthyTeeth/${number}C.glb`, resolve, undefined, reject);
        })

        const model = gltf.scene.children[0];
        model.position.set(0, 0, 0);
        model.rotation.set(0, 0, 0);
        model.scale.set(0.75, 0.75, 0.75);
        model.name = `${number}C`;
        const originalMaterial = model.material.clone();
        originalMaterial.transparent = true;
        originalMaterial.opacity = 0;
        originalMaterial.depthWrite = false;
        originalMaterial.renderOrder = 1;
        originalMaterial.roughness = 1;
        originalMaterial.metalness = 0;
        originalMaterial.side = THREE.FrontSide;
        originalMaterial.depthWrite = true;
        model.userData = {
            number: number,
            name: `${number}C`,
            isInteractive: true,
            originalMaterial: originalMaterial
        };

        if (number >= 9 && number <= 24) {
            const originalScale = new THREE.Vector3();
            model.getWorldScale(originalScale);
            model.scale.set(1, 1, 1);
            model.updateMatrixWorld(true);
            model.scale.set(
            -Math.abs(originalScale.x),
            -Math.abs(originalScale.y),
            -Math.abs(originalScale.z)
            );
        }

        pointer.add(model);

        chartingMDChildPointersRef.current.set(parseInt(number, 10), model);
    };

    useEffect(() => {
        if (chartingWatchToolPointersRef?.current) {
            // Clear any existing children from pointers
            chartingWatchToolPointersRef.current.forEach(pointer => {
                while (pointer.children.length > 0) {
                const child = pointer.children[0];
                pointer.remove(child);
                if (child.geometry) child.geometry.dispose();
                if (child.material) {
                    if (Array.isArray(child.material)) {
                    child.material.forEach(material => material.dispose());
                    } else {
                    child.material.dispose();
                    }
                }
                }
            });

            // Load Eyes
            Object.entries(patientTeeth).forEach(([number, toothData]) => {
                if (number !== 'patientType' && number !== 'patientId'){
                    const pointer = chartingWatchToolPointersRef.current.get(parseInt(number, 10));
                    if (pointer && toothData.marked_as_watched){
                        loadEyesModel(number, pointer);
                    }
                }
            });
        }
    }, []);

    const handleWatchTooth = (toothNumber) => {

        const pointer = chartingWatchToolPointersRef.current.get(parseInt(toothNumber, 10));
    
        if (patientTeeth[toothNumber]?.marked_as_watched) {
            // If already marked as watched, remove the watch
            setPatientTeeth(prevTeeth => {
                const newTeeth = { ...prevTeeth };
                newTeeth[toothNumber].marked_as_watched = false;
                return newTeeth;
            });
    
            // remove eyes
            for (let i = pointer.children.length - 1; i >= 0; i--) {
                const child = pointer.children[i];
                if (child.name === `eyes_${toothNumber}`) {
                    pointer.remove(child);
    
                    // Dispose of geometry and materials
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(material => material.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                }
            }
        } else {
            // If not marked, set it to true
            setPatientTeeth(prevTeeth => {
                const newTeeth = { ...prevTeeth };
                newTeeth[toothNumber].marked_as_watched = true;
                return newTeeth;
            });
    
            // add eyes
            loadEyesModel(toothNumber, pointer);
        }
    }

    const handleMixedDentation = (toothNumber) => {
        if (toothNumber < 4 || (toothNumber > 13 && toothNumber < 20) || toothNumber > 29) return;
        
        const pointer = chartingMDChildPointersRef.current.get(parseInt(toothNumber, 10));

        if (patientTeeth[toothNumber]?.mixed_dentition) {
            setPatientTeeth(prevTeeth => {
                const newTeeth = { ...prevTeeth };
                newTeeth[toothNumber].mixed_dentition = false;
                return newTeeth;
            });

            for (let i = pointer.children.length - 1; i >= 0; i--) {
                const child = pointer.children[i];
                if (child.name === `${toothNumber}C`) {
                    pointer.remove(child);
    
                    // Dispose of geometry and materials
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(material => material.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                }
            }
        } else {
            // If not marked, set it to true
            setPatientTeeth(prevTeeth => {
                const newTeeth = { ...prevTeeth };
                newTeeth[toothNumber].mixed_dentition = true;
                return newTeeth;
            });
    
            // add eyes
            loadChildTooth(toothNumber, pointer);
        }

    };

    useEffect(() => {
        if (watchTooth) {
            handleWatchTooth(watchTooth);
            setWatchTooth(null);
        }
    }, [watchTooth])

    useEffect(() => {
        if (mixedDentation) {
            handleMixedDentation(mixedDentation);
            setMixedDentation(null);
        }
    }, [mixedDentation])

    return null;

};

// Pre-load the model
useGLTF.preload(MODELS.CHARTING);

export default Charting;