# Message Handling System

This document outlines the message handling system for communication between UPOD and the iframe.

## Overview

The message handling system consists of:

1. **TeethContext**: Manages the state of patient teeth data
2. **teethMessageHandler.js**: Utility functions for sending and receiving messages
3. **useTeethMessages.js**: A hook for using the message handlers in components

## Message Types

### Incoming Messages (UPOD to Iframe)

| Message Type                  | Description                     | Data                   |
| ----------------------------- | ------------------------------- | ---------------------- |
| `initialize_teeth`            | Initialize teeth data           | Array of teeth objects |
| `clear_teeth`                 | Clear all teeth data            | None                   |
| `treatment_selected`          | A treatment has been selected   | Treatment object       |
| `charted_treatment_saved`     | A treatment has been saved      | `ctId`                 |
| `eraser_state_change`         | Eraser tool state changed       | `active` (boolean)     |
| `charted_treatment_deleted`   | A treatment has been deleted    | `ctId`                 |
| `charted_treatment_removed`   | A treatment has been removed    | `ctId`, `position`     |
| `tooth_removed`               | A tooth has been removed        | `position`             |
| `mixed_dentition_saved`       | Mixed dentition has been saved  | `position`, `active`   |
| `bridge_treatment_saved`      | Bridge treatment has been saved | `ctId`                 |
| `charted_treatment_completed` | A treatment has been completed  | `ctId`, `position`     |
| `initialize_single_tooth`     | Initialize a single tooth       | Tooth object           |
| `watch_tooth`                 | Watch a tooth                   | `position`, `watched`  |

### Outgoing Messages (Iframe to UPOD)

| Message Type                          | Description                                   | Data                             |
| ------------------------------------- | --------------------------------------------- | -------------------------------- |
| `teeth_initialized`                   | Teeth data has been initialized               | None                             |
| `teeth_cleared`                       | Teeth data has been cleared                   | None                             |
| `position_and_surfaces_selected`      | Position and surfaces have been selected      | `position`, `surfaces`           |
| `charted_treatment_removed`           | A treatment has been removed                  | `ctId`                           |
| `remove_tooth`                        | Remove a tooth                                | `position`                       |
| `mixed_dentition`                     | Set mixed dentition                           | `active`, `position`             |
| `bridge_treatment_positions_selected` | Bridge treatment positions have been selected | `start_position`, `end_position` |
| `treatment_selected`                  | A treatment has been selected                 | Treatment object                 |
| `add_annotations`                     | Add annotations                               | `note`, `ctId`                   |
| `view_tooth_history`                  | View tooth history                            | `position`                       |
| `tooth_rendered`                      | Tooth has been rendered                       | None                             |
| `charted_treatment_completed`         | A treatment has been completed                | `ctId`, `position`               |
| `watch_tooth`                         | Watch a tooth                                 | `position`, `watched`            |

## Usage

### Initialize Message Listener

```jsx
import { useTeethMessages } from "./hooks/useTeethMessages";

function App() {
  // Initialize teeth message handlers
  const teethMessages = useTeethMessages();

  // ...
}
```

### Send Messages

```jsx
// Select position and surfaces
teethMessages.selectPositionAndSurfaces("UL8", ["Mesial", "Distal"]);

// Remove a treatment
teethMessages.removeTreatment("123");

// Remove a tooth
teethMessages.removeTooth("UL8");

// Set mixed dentition
teethMessages.setMixedDentition(true, "UL8");

// Select bridge treatment positions
teethMessages.selectBridgeTreatmentPositions("UL2", "UL5");

// Select a treatment
teethMessages.selectTreatment({
  id: 123,
  name: "Filling",
  full_tooth_treatment: false,
});

// Add annotations
teethMessages.addAnnotations("Note text", "123");

// View tooth history
teethMessages.viewToothHistory("UL8");

// Send tooth rendered
teethMessages.toothRendered();

// Complete a treatment
teethMessages.completeTreatment("123", "UL8");

// Watch a tooth
teethMessages.watchTooth("UL8", true);
```

## Data Structures

### Tooth Object

```javascript
{
  position: "UL8",
  position_number: 8,
  status: "healthy",
  lastTreatment: "2024-04-20",
  notes: "Note text",
  marked_as_missing: false,
  treatments: [
    {
      id: "123",
      ctId: "456",
      name: "Filling",
      full_tooth_treatment: false,
      patient_treatment: false,
      remove_tooth_when_completed: false,
      remove_treatment_when_completed: false,
      bridge_treatment: false,
      missing_tooth_indicator: false,
      mixed_dentition: false,
      need_default_tooth: false,
      created_at: "2024-04-20",
      completed_at: null,
      surfaces: {
        "DistalOcclusal": { "decaySeverity": 1, "fillingSize": 1 }
      }
    }
  ]
}
```

### Treatment Object

```javascript
{
  id: "123",
  ctId: "456",
  name: "Filling",
  full_tooth_treatment: false,
  patient_treatment: false,
  remove_tooth_when_completed: false,
  remove_treatment_when_completed: false,
  bridge_treatment: false,
  missing_tooth_indicator: false,
  mixed_dentition: false,
  created_at: "2024-04-20",
  completed_at: null,
  surfaces: {
    "DistalOcclusal": { "decaySeverity": 1, "fillingSize": 1 }
  }
}
```
