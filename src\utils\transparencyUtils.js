import * as THREE from 'three';

/**
 * Ensures all materials in a model are transparent
 * @param {THREE.Object3D} model - The model to process
 * @param {number} opacity - The opacity to apply (0-1)
 */
export const ensureAllMaterialsTransparent = (model, opacity = 0.8) => {
  if (!model) return;
  
  model.traverse(obj => {
    if (obj.isMesh && obj.material) {
      // Handle array of materials
      if (Array.isArray(obj.material)) {
        obj.material.forEach((mat, index) => {
          if (!mat.transparent) {
            const newMat = mat.clone();
            newMat.transparent = true;
            newMat.opacity = opacity;
            obj.material[index] = newMat;
          }
        });
      } 
      // Handle single material
      else if (!obj.material.transparent) {
        const newMat = obj.material.clone();
        newMat.transparent = true;
        newMat.opacity = opacity;
        obj.material = newMat;
      }
    }
  });
};

/**
 * Applies transparency to all treatment materials in a model
 * @param {THREE.Object3D} model - The model to process
 * @param {Array} treatments - Array of treatments
 * @param {Function} getTreatmentVisibility - Function to check treatment visibility
 * @param {string} toothNumber - The tooth number
 */
export const applyTransparencyToAllTreatments = (model, treatments, getTreatmentVisibility, toothNumber) => {
  if (!model || !treatments || !treatments.length) return;
  
  // Sort treatments by creation date (newest first)
  const sortedTreatments = [...treatments].sort((a, b) => {
    const dateA = new Date(a.created_at || 0);
    const dateB = new Date(b.created_at || 0);
    return dateB - dateA; // Newest first
  });
  
  // Process each mesh in the model
  model.traverse(obj => {
    if (obj.isMesh && obj.material) {
      // Get the part name from the object or its parent
      const partName = obj.name.split('_')[0] || 
                       (obj.parent ? obj.parent.name.split('_')[0] : '');
      
      // Find the treatment that corresponds to this part
      const treatmentForPart = sortedTreatments.find(t => t.name === partName);
      
      if (treatmentForPart) {
        // Find the index of this treatment in the sorted array
        const index = sortedTreatments.indexOf(treatmentForPart);
        
        // Calculate opacity based on index (newest = 80%, next = 60%, etc.)
        const opacity = Math.max(0.15, 0.8 - index * 0.2);
        
        // Apply transparency
        if (Array.isArray(obj.material)) {
          obj.material.forEach((mat, i) => {
            const newMat = mat.clone();
            newMat.transparent = true;
            newMat.opacity = opacity;
            obj.material[i] = newMat;
          });
        } else {
          const newMat = obj.material.clone();
          newMat.transparent = true;
          newMat.opacity = opacity;
          obj.material = newMat;
        }
        
        // Check if this treatment should be visible
        if (getTreatmentVisibility) {
          const isVisible = getTreatmentVisibility(
            toothNumber, 
            treatmentForPart.Id || treatmentForPart.id
          );
          obj.visible = isVisible;
        }
      }
    }
  });
};
