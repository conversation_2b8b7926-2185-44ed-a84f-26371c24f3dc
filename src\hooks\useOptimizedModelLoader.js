import { useState, useRef, useEffect, useCallback } from 'react';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { modelCache, animationCache } from '../utils/modelUtils';
import { cloneModel } from '../utils/modelUtils';

/**
 * Custom hook for optimized model loading with caching
 * @param {string} resourcePath - Base resource path for models
 * @returns {Object} - Model loading utilities
 */
export const useOptimizedModelLoader = (resourcePath = './TreatmentsV4/') => {
  const loaderRef = useRef(new GLTFLoader());
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const mountedRef = useRef(true);
  
  // Initialize loader
  useEffect(() => {
    loaderRef.current.setResourcePath(resourcePath);
    loaderRef.current.setCrossOrigin('anonymous');
    
    return () => {
      mountedRef.current = false;
    };
  }, [resourcePath]);
  
  /**
   * Load a model with caching
   * @param {string} modelPath - Path to the model
   * @param {string} viewType - Current view type for cloning
   * @returns {Promise<Object>} - Loaded model data
   */
  const loadModel = useCallback(async (modelPath, viewType) => {
    if (!mountedRef.current) return null;
    
    try {
      let gltf;
      
      // Check cache first
      if (modelCache.has(modelPath)) {
        gltf = { 
          scene: cloneModel(modelCache.get(modelPath).scene, viewType),
          animations: animationCache.has(modelPath) 
            ? animationCache.get(modelPath).map(a => a.clone())
            : []
        };
        return gltf;
      }
      
      // Load if not in cache
      setIsLoading(true);
      setProgress(0);
      
      gltf = await new Promise((resolve, reject) => {
        loaderRef.current.load(
          modelPath,
          resolve,
          (event) => {
            if (event.lengthComputable) {
              const progressValue = Math.round((event.loaded / event.total) * 100);
              setProgress(progressValue);
            }
          },
          reject
        );
      });
      
      // Cache the model
      if (!modelCache.has(modelPath)) {
        modelCache.set(modelPath, {
          scene: cloneModel(gltf.scene, viewType)
        });
        
        if (gltf.animations?.length) {
          animationCache.set(
            modelPath,
            gltf.animations.map(a => a.clone())
          );
        }
      }
      
      setIsLoading(false);
      return gltf;
    } catch (error) {
      setIsLoading(false);
      throw error;
    }
  }, []);
  
  /**
   * Preload a model into cache
   * @param {string} modelPath - Path to the model
   * @param {string} viewType - Current view type for cloning
   */
  const preloadModel = useCallback(async (modelPath, viewType) => {
    if (modelCache.has(modelPath)) return;
    
    try {
      await loadModel(modelPath, viewType);
    } catch (error) {
      // Silently fail for preloading
    }
  }, [loadModel]);
  
  return {
    loadModel,
    preloadModel,
    isLoading,
    progress,
    loader: loaderRef.current
  };
};
