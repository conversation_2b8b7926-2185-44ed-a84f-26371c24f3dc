import * as THREE from "three";

export const createSkullMaterial = () =>
  new THREE.MeshStandardMaterial({
   transparent: true,
    opacity: 0.35,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    emissiveIntensity: 0.15,
    depthWrite: false,  // Important for transparency
    depthTest: true,    // Keep depth testing
    roughness: 0.4,
    metalness: 0.1,
  });

export const createGumMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0xffb39f, // Pink color for gums
    transparent: true,
    opacity: 0.35,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    emissiveIntensity: 0.15,
    depthWrite: false,  // Important for transparency
    depthTest: true,    // Keep depth testing
    roughness: 0.4,
    metalness: 0.1,
  });

export const createDefaultMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0xffffff,
    metalness: 0.3,
    roughness: 0.2,
    transparent: false,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    depthWrite: true,
  });

export const createHighlightMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0xffd700,
    metalness: 0.4,
    roughness: 0.2,
    transparent: false,
    emissive: 0xffe57f,
    emissiveIntensity: 0.6,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    depthWrite: true,
  });

export const createMetalMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(176 / 255, 176 / 255, 176 / 255),
    metalness: 1,
    roughness: 0.2,
  });

export const createGoldMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(207 / 255, 152 / 255, 32 / 255),
    metalness: 1,
    roughness: 0.2,
  });

export const createCompositeMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(250 / 255, 250 / 255, 250 / 255),
    metalness: 0,
    roughness: 0.1,
  });

export const createSilverMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(211 / 255, 216 / 255, 217 / 255),
    metalness: 1,
    roughness: 0.1,
  });

export const createTransparentMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(250 / 255, 250 / 255, 250 / 255),
    metalness: 0,
    roughness: 0.1,
    transparent: true,
    opacity: 0.5,
  });

export const createMatMaterial = (roughness) =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(240 / 255, 240 / 255, 240 / 255),
    metalness: 0,
    roughness,
  });

export const createBlackMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(0 / 255, 0 / 255, 0 / 255),
    metalness: 0,
    roughness: 1,
  });

export const createRoughWhiteMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(125/255, 125/255, 125/255),
    metalness: 0,
    roughness: 1,
  });

export const createMaterial1 = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(240/255, 240/255, 240/255),
    metalness: 0,
    roughness: 0,
  });

export const createMaterial2 = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(240/255, 240/255, 240/255),
    metalness: 0,
    roughness: 0.1,
  });

export const createMaterial3 = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(240/255, 240/255, 240/255),
    metalness: 0,
    roughness: 0.2,
  });

export const createMaterial4 = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(240/255, 240/255, 240/255),
    metalness: 0,
    roughness: 0.3,
  });

export const createMaterial5 = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(240/255, 240/255, 240/255),
    metalness: 0,
    roughness: 0.4,
  });

export const createMaterial6 = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(240/255, 240/255, 240/255),
    metalness: 0,
    roughness: 0.5,
  });