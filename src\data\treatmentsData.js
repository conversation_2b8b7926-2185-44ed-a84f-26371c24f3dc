export const treatmentsData = [
  {
    treatment_name: "Abcess",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: "Full tooth",
    video_link:
      "No Video just removal. Dynamically should be on other videos if present on patient",
    meshes_based_on_treatment: null,
    comments: "abcess seperate image",
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: "root",
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "All Done",
    state_a_state_b_progress: "In progress - need healthy teeth",
    state_c_progress: "Done",
    need_file_update: "Done",
  },
  {
    treatment_name: "Abcess Removal",
    adult: "Yes",
    child: "Yes",
    example: "No Charting",
    charting_type: "Full tooth",
    video_link: "https://www.youtube.com/watch?v=SxmPHDSrKW8",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: null,
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Amalgam Filling",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: "tooth surfaces",
    video_link: "https://www.youtube.com/watch?v=Oz6qEdf_ID0",
    meshes_based_on_treatment: "Metal",
    comments: null,
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Metal",
      properties: {
        Albedo: [176176176],
        Roughess: 0.2,
        Metallic: 1.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "All Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Gold Filling",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: null,
    video_link: "Same as amalgam filling but gold",
    meshes_based_on_treatment: "Gold",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Gold",
      properties: {
        Albedo: [207, 152, 32],
        Roughess: 0.2,
        Metallic: 1.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "All Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Composite Filling",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: "tooth surfaces",
    video_link: "Same as every other filling but just white",
    meshes_based_on_treatment: "Composite",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Composite",
      properties: {
        Albedo: [250250250],
        Roughess: 0.1,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "All Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Glass Ionomer Filling",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: null,
    video_link: "Same as coposite filling",
    meshes_based_on_treatment: "Glass",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Glass",
      properties: {
        Albedo: [250250250],
        Roughess: 0.1,
        Metallic: 0.0,
        Transparency: "on",
        Opacity: 0.5,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "All Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Veneer",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=TPqrhW8Zhv0",
    meshes_based_on_treatment: "Mat 6",
    comments: null,
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 6",
      properties: {
        Albedo: [240240240],
        Roughess: 0.5,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "In progress - add material",
    state_c_progress: "Done",
    need_file_update: "ReDo",
  },
  {
    treatment_name: "Emax Veneer",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tooth",
    video_link: "Exaclty the same as veneer",
    meshes_based_on_treatment: "Mat 1",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 1",
      properties: {
        Albedo: [240240240],
        Roughess: 0.0,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "In progress - add material",
    state_c_progress: "Done",
    need_file_update: "ReDo",
  },
  {
    treatment_name: "Empress Veneer",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tooth",
    video_link: "Exaclty the same as veneer",
    meshes_based_on_treatment: "Mat 2",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 2",
      properties: {
        Albedo: [240240240],
        Roughess: 0.1,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "In progress - add material",
    state_c_progress: "Done",
    need_file_update: "ReDo",
  },
  {
    treatment_name: "Composite Veneer",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tooth",
    video_link:
      "https://www.shutterstock.com/video/clip-**********-dental-veneers-placement-over-teeth-happy-smile",
    meshes_based_on_treatment: "Composite",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Composite",
      properties: {
        Albedo: [250250250],
        Roughess: 0.1,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "In progress - add material",
    state_c_progress: "Done",
    need_file_update: "ReDo",
  },
  {
    treatment_name: "Gold Veneer",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as veneer but gold",
    meshes_based_on_treatment: "Gold",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Gold",
      properties: {
        Albedo: [207, 152, 32],
        Roughess: 0.2,
        Metallic: 1.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "In progress - add material",
    state_c_progress: "Done",
    need_file_update: "ReDo",
  },
  {
    treatment_name: "Porcelain Veneer",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as veneer",
    meshes_based_on_treatment: "Mat 3",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 3",
      properties: {
        Albedo: [240240240],
        Roughess: 0.2,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "In progress - add material",
    state_c_progress: "Done",
    need_file_update: "ReDo",
  },
  {
    treatment_name: "Silver Veneer",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as veneer",
    meshes_based_on_treatment: "Metal",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Metal",
      properties: {
        Albedo: [176176176],
        Roughess: 0.2,
        Metallic: 1.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "In progress - add material",
    state_c_progress: "Done",
    need_file_update: "ReDo",
  },
  {
    treatment_name: "Temporary Veneer",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as veneer",
    meshes_based_on_treatment: "Mat 4",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 4",
      properties: {
        Albedo: [240240240],
        Roughess: 0.3,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "In progress - add material",
    state_c_progress: "Done",
    need_file_update: "ReDo",
  },
  {
    treatment_name: "Zirconia Veneer",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as veneer",
    meshes_based_on_treatment: "Mat 5",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 5",
      properties: {
        Albedo: [240240240],
        Roughess: 0.4,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "In progress - add material",
    state_c_progress: "Done",
    need_file_update: "ReDo",
  },
  {
    treatment_name: "CrownForImplant",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "CrownForImplant & ImplantForCrown",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "CrownForModifiedTooth",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "ModifiedToothForCrown & CrownForModedTooth",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Bonded Crown",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tooth",
    video_link: "Same as crown video just different type of crown",
    meshes_based_on_treatment: "Mat 1",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 1",
      properties: {
        Albedo: [240240240],
        Roughess: 0.0,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Cerec Crown",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link:
      "Same as normal white crown video - https://www.youtube.com/watch?v=90Ee1W3FVG8",
    meshes_based_on_treatment: "Mat 2",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 2",
      properties: {
        Albedo: [240240240],
        Roughess: 0.1,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Composite Crown",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tooth",
    video_link:
      "Same as normal white crown video - https://www.youtube.com/watch?v=90Ee1W3FVG8",
    meshes_based_on_treatment: "Mat 3",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 3",
      properties: {
        Albedo: [240240240],
        Roughess: 0.2,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Emax Crown",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tooth",
    video_link: "Same as every other crown",
    meshes_based_on_treatment: "Mat 4",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 4",
      properties: {
        Albedo: [240240240],
        Roughess: 0.3,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Empress Crown",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tooth",
    video_link:
      "Exaclty the same as every other crown just a different colour.",
    meshes_based_on_treatment: "Mat 5",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 5",
      properties: {
        Albedo: [240240240],
        Roughess: 0.4,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Gold Crown",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as crown but gold",
    meshes_based_on_treatment: "Gold",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Gold",
      properties: {
        Albedo: [207, 152, 32],
        Roughess: 0.2,
        Metallic: 1.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Metal Crown",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as Crown but metal",
    meshes_based_on_treatment: "Metal",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Metal",
      properties: {
        Albedo: [176176176],
        Roughess: 0.2,
        Metallic: 1.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Porcelain Bonded Crown",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as crown",
    meshes_based_on_treatment: "Mat 6",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 6",
      properties: {
        Albedo: [240240240],
        Roughess: 0.5,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Porcelain Crown",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as crown",
    meshes_based_on_treatment: "Mat 7",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 7",
      properties: {
        Albedo: [240240240],
        Roughess: 0.6,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Silver Crown",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as crown but metal/silver",
    meshes_based_on_treatment: "Silver",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Silver",
      properties: {
        Albedo: [211216217],
        Roughess: 0.1,
        Metallic: 1.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Zirconia Crown",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as crown",
    meshes_based_on_treatment: "Mat 8",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 8",
      properties: {
        Albedo: [240240240],
        Roughess: 0.7,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Zygo Implant Crown",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as implant crown but on zygo",
    meshes_based_on_treatment: "Mat 9",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Mat 9",
      properties: {
        Albedo: [240240240],
        Roughess: 0.8,
        Metallic: 0.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Zygomatic Implant",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=QaPz0NWcSDM",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "need to be able to chart implants on 5,6,7,8",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Zygo Implant Bridge",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as zygomatic implant",
    meshes_based_on_treatment: null,
    comments: "implant and bridge separate",
    root: "no",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "Just the bridge for zygo",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Bridge",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full tooth",
    video_link: "https://www.youtube.com/watch?v=9jptx6l9PL4",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "teeth bridge",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Bonded Bridge Pontic",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tooth",
    video_link: null,
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "teeth bridge",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "Done",
    state_c_progress: "Done",
    need_file_update: "Done",
  },
  {
    treatment_name: "Gold Bridge Pontic",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=OcxEjFVBG54",
    meshes_based_on_treatment: "Gold",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Gold",
      properties: {
        Albedo: [207, 152, 32],
        Roughess: 0.2,
        Metallic: 1.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: "teeth bridge",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Porcelain Bridge Pontic",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as bridge",
    meshes_based_on_treatment: "Metal",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Metal",
      properties: {
        Albedo: [176176176],
        Roughess: 0.2,
        Metallic: 1.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: "teeth bridge",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Porcelain Bonded Bridge Pontic",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as crown",
    meshes_based_on_treatment: "Metal",
    comments: null,
    root: "no",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Metal",
      properties: {
        Albedo: [176176176],
        Roughess: 0.2,
        Metallic: 1.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: "teeth bridge",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Final Hybrid Bridge",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tooth",
    video_link:
      "Same as every other bridge just a different colour on charting - https://www.youtube.com/watch?v=KBXr8V7raLM",
    meshes_based_on_treatment: null,
    comments: "implant and bridge seperate",
    root: "no",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "implant and bridge seperate",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant Bridge",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: "implant and bridge separate",
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "implant and bridge separate",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant Bridge - Cerec",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: "implant and bridge separate",
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "implant and bridge separate",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant Bridge - Emax",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: "implant and bridge separate",
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "implant and bridge separate",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant Bridge - Porcelain",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=99orXY_RqqQ",
    meshes_based_on_treatment: null,
    comments: "implant and bridge separate",
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "implant and bridge separate",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant Bridge - Zirconia",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: "implant and bridge separate",
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "implant and bridge separate",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Decay",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: "Full Tooth",
    video_link:
      "No Video just removal. Dynamically should be on other videos if present on patient",
    meshes_based_on_treatment: "filling files",
    comments: null,
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: { name: "filling files", properties: null },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "All Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Arrested Caries",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: "tooth surfaces",
    video_link:
      "No Video just removal. Dynamically should be on other videos if present on patient",
    meshes_based_on_treatment: "filling files",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: { name: "filling files", properties: null },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "All Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Cavity",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: "Full tooth",
    video_link:
      "No Video just removal. Dynamically should be on other videos if present on patient",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "All Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Caries",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: "Full tooth",
    video_link:
      "No Video just removal. Dynamically should be on other videos if present on patient",
    meshes_based_on_treatment: "filling files with textures",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: { name: "filling files with textures", properties: null },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "All Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Early Caries",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: "Full Tooth",
    video_link:
      "No Video just removal. Dynamically should be on other videos if present on patient",
    meshes_based_on_treatment: "filling files with textures",
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: { name: "filling files with textures", properties: null },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "All Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant - Partial Denture",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as full implant denture but fewer teeth",
    meshes_based_on_treatment: null,
    comments: "implant and denture seperate",
    root: "no",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "implant and denture seperate",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant Retained Denture",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as partial but with all teeth",
    meshes_based_on_treatment: null,
    comments: "implant and denture seperate",
    root: "no",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "implant and denture seperate",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant - Full Denture",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=uiSVd2kx9ag",
    meshes_based_on_treatment: null,
    comments: "implant and denture seperate",
    root: "no",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "implant and denture seperate",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Apicectomy/Periradicular Surgery",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full tooth",
    video_link: "https://www.youtube.com/watch?v=2sE5IoP4DWg",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "Done",
    state_c_progress: "Done",
    need_file_update: "Done",
  },
  {
    treatment_name: "Block Grafting",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full tooth",
    video_link: "https://www.youtube.com/watch?v=VjQs3KQcqy0",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments:
      "Same file as BoneGrafting just need to be able to chart more than one at a time.",
    development_comments: "add texture and normal for all states",
    modeling_progress: "Done",
    state_a_state_b_progress: "in progress",
    state_c_progress: "Done - only on FV",
    need_file_update: "fix pos",
  },
  {
    treatment_name: "Bone Expansion",
    adult: "Yes",
    child: "same as below",
    example: null,
    charting_type: "Full tooth",
    video_link: "https://www.youtube.com/watch?v=lID4WC088HI",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: null,
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Bone Grafting",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tootth",
    video_link: "https://youtube.com/shorts/wOlMwaTspeI?si=AWS0zJBYrHLyd5Dw",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments:
      "If possible dont do changes on rotation For StateC pls add the textures on the normal and difuse slot.",
    development_comments:
      "add texture and normal for all states + dont flip right side for State C",
    modeling_progress: "Done",
    state_a_state_b_progress: "in progress",
    state_c_progress: "Done - only on FV",
    need_file_update: "fix pos",
  },
  {
    treatment_name: "Clasp",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments:
      "Crown",
    material: null,
    modeling_comments: "use State A for charting",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "in progress - need healthy teeth",
    state_c_progress: "Done",
    need_file_update: "Done",
  },
  {
    treatment_name: "Closed Gap",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tooth",
    video_link:
      "Just need to show 2 3D arrows pointing inwards where the tooth would be doesn’t need to be on a video.",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "in progress - need add black material",
    state_c_progress: "Done",
    need_file_update: "Done",
  },
  {
    treatment_name: "Composite Bonding",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tooth",
    video_link: "https://www.youtube.com/shorts/FpGHWzvde2I",
    meshes_based_on_treatment: null,
    comments: null,
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "Veneer File",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Core Buildup, Including Any Pins",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=iLNAtkTUxrw",
    meshes_based_on_treatment: null,
    comments: "core build up, pins and post are all seperate charting images",
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments:
      "core build up, pins and post are all seperate charting images",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "Done",
    state_c_progress: "Done",
    need_file_update: null,
  },
  {
    treatment_name: "Drifted Left",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments:
      "need to move them by position rather than loading new teeth",
    development_comments: null,
    modeling_progress: "AllDone",
    state_a_state_b_progress: "in progress",
    state_c_progress: "in progress",
    need_file_update: "fix pos",
  },
  {
    treatment_name: "Drifted Right",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "AllDone",
    state_a_state_b_progress: "in progress",
    state_c_progress: "in progress",
    need_file_update: "fix pos",
  },
  {
    treatment_name: "Dropped Sinus",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tooth",
    video_link: "No Video just a video for sinus lift",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "File name is (SinusDrop) morph 0=Dropped Sinus",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "External Sinus Lift",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=wRIv-iEo6hc",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "File name is (SinusDrop) morph 1=External Sinus Lift",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Extraction",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: "Full Tooth",
    video_link:
      "https://www.shutterstock.com/video/clip-**********-extraction-molar-tooth-damaged-by-caries-medically",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: "Yes",
    material: null,
    modeling_comments: "add TV extraction files for TV teeth in State C",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "in progress - need healthy teeth",
    state_c_progress: "Done",
    need_file_update: null,
  },
  {
    treatment_name: "Fissure Sealant - Per Tooth",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tooth",
    video_link: "https://www.youtube.com/watch?v=WAHTQGYS-1g",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments:
      "Crown",
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "in progress - need healthy teeth",
    state_c_progress: "Done",
    need_file_update: "Done",
  },
  {
    treatment_name: "Flexi Denture",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: "Full Tooth",
    video_link: "https://www.youtube.com/watch?v=iLNAtkTUxrw",
    meshes_based_on_treatment: null,
    comments: null,
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments:
      "use the one pointer for all teeth for A and B - dont add for State C",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "in progress",
    state_c_progress: "doesnt exist?",
    need_file_update: null,
  },
  {
    treatment_name: "Fractured Tooth - Large",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=CvqOHCHMbCU",
    meshes_based_on_treatment: null,
    comments: null,
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "Done",
    state_c_progress: "Done",
    need_file_update: "Done",
  },
  {
    treatment_name: "Fractured Tooth - Small",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: null,
    video_link: "Same as fractured tooth",
    meshes_based_on_treatment: null,
    comments: null,
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "Done",
    state_c_progress: "Done",
    need_file_update: "Done",
  },
  {
    treatment_name: "Full Mouth Clearance",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link:
      "Same as extraction but its all remaining teeth being extracted",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: null,
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Full Upper & Lower Acrylic Denture",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=2NmFnu2LPO0",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Full Upper & Lower Chrome Denture Fit",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/shorts/N8aAnJnSYaA",
    meshes_based_on_treatment: "Metal",
    comments: "make partial variant",
    root: "no",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Metal",
      properties: {
        Albedo: [176176176],
        Roughess: 0.2,
        Metallic: 1.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Upper & Lower Partial Chrome Denture",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: {
      name: "Metal",
      properties: {
        Albedo: [176176176],
        Roughess: 0.2,
        Metallic: 1.0,
        Transparency: null,
        Opacity: null,
      },
    },
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Impacted tooth",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=8TLExDoFA8Q",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "Done",
    state_c_progress: "Done",
    need_file_update: null,
  },
  {
    treatment_name: "Implant Abutment",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "Maz:Why are we charitng this seperatly",
    development_comments: null,
    modeling_progress: null,
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=8gVfdyASewA",
    meshes_based_on_treatment: null,
    comments: "implant and crown separate",
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "Implant",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant Crown",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: "implant and crown separate",
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "Crown for implant",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant Crown - Cerec",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: "implant and crown separate",
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "Crown for implant",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant Crown - Emax",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: "implant and crown separate",
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "Crown for implant",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant Crown - Porcelain",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=8gVfdyASewA",
    meshes_based_on_treatment: null,
    comments: "implant and crown separate",
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "Crown for implant",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant Crown - Zirconia",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: "implant and crown separate",
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "Crown for implant",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant Crown - Gold",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: "implant and crown separate",
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "Crown for implant",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Implant Crown - Metal",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: "implant and crown separate",
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "Crown for implant",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Inlay",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=KmMpI6OJQg0",
    meshes_based_on_treatment: null,
    comments: null,
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "SameFileAsOnLay",
    development_comments: null,
    modeling_progress: "AllDone",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Onlay",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: null,
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments:
      "OnlayTooth and Onlay. Treat them as you would the decay and filling respectively. ",
    development_comments: null,
    modeling_progress: "AllDone",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Inlay/Onlay - Gold",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=edEqizdlpkg",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Inlay/Onlay - Porcelain/Ceramic",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=KP0lJLFfR8A",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Maryland Bridge",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=GbYc4B_77NI",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "CheckWithDevs",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "in progress",
    state_c_progress: "in progress - only on FV",
    need_file_update:
      "state A missing teeth 20 & 21 and fix pos of some metal pieces - state B is perfect - state C fix pos + note: state C has no teeth whereas state A and B has teeth included in the files",
  },
  {
    treatment_name: "Maryland Wing",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as maryland bridge",
    meshes_based_on_treatment: null,
    comments: null,
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "CheckWithDevs",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "Done",
    state_c_progress: "in progress - only on FV",
    need_file_update: "Done",
  },
  {
    treatment_name: "Missing Tooth",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "Done",
    state_c_progress: "Done",
    need_file_update: "Done",
  },
  {
    treatment_name: "Partially Erupted Tooth",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "No video needed",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "Done",
    state_c_progress: "Done",
    need_file_update: "Done",
  },
  {
    treatment_name: "Pin Retention",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "No video needed",
    meshes_based_on_treatment: null,
    comments: null,
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "in progress - add transparency",
    state_c_progress: "Done",
    need_file_update: "Done",
  },
  {
    treatment_name: "Post & Core",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=p9YT_J31v80",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: "Any",
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "in progress - need healthy teeth",
    state_c_progress: "in progress - fix mesh depth issue to show the metal",
    need_file_update: "fix pos",
  },
  {
    treatment_name: "Present",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: null,
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "RootCanalTreatment",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/shorts/14z8m0lnUog",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: "Any",
    material: null,
    modeling_comments: "No Need",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "in progress - add transparency",
    state_c_progress: "in progress",
    need_file_update: "remove healthy teeth",
  },
  {
    treatment_name: "Reset Tooth",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: "Full healthy tooth",
    video_link: null,
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: null,
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Retained Root",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: null,
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "Done",
    state_c_progress: "Done",
    need_file_update: null,
  },
  {
    treatment_name: "Root Filled",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as root canal",
    meshes_based_on_treatment: null,
    comments: null,
    root: "no",
    crown: "no",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: "Any",
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: null,
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Temporary Bridge Unit",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as bridge",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Temporary Crown",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as crown",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Temporary Denture",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as denture",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Temporary Filling",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as GI filling",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "TIAD Both Arches",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "https://www.youtube.com/watch?v=KBXr8V7raLM",
    meshes_based_on_treatment: null,
    comments: "implants and bridge separate",
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "implants and bridge separate",
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Tooth Addition to a Acrylic Denture",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as denture but adding an extra tooth",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Tooth Addition to a Chrome Denture",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "Same as denture but adding an extra tooth",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
  },
  {
    treatment_name: "Unerupted or exfoliated teeth",
    adult: "Yes",
    child: null,
    example: null,
    charting_type: null,
    video_link: "No video needed",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: "Two veriants A and B. Both work for C",
    development_comments: "use State A for charting",
    modeling_progress: "Done",
    state_a_state_b_progress: "in progress",
    state_c_progress: "in progress",
    need_file_update: "Done",
  },
  {
    treatment_name: "Watch Tooth",
    adult: "Yes",
    child: "Yes",
    example: null,
    charting_type: null,
    video_link: "No video needed",
    meshes_based_on_treatment: null,
    comments: null,
    root: null,
    crown: null,
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "Done",
    state_a_state_b_progress: "Done",
    state_c_progress: "Done",
    need_file_update: null,
  },
  {
    treatment_name: "Filling",
    adult: "Yes",
    child: "Yes",
    root: "yes",
    crown: "yes",
    add_healthy_tooth_if_the_following_doesnt_exist_in_other_treatments: null,
    material: null,
    modeling_comments: null,
    development_comments: null,
    modeling_progress: "All Done",
    state_a_state_b_progress: null,
    state_c_progress: null,
    need_file_update: null,
    example: null,
    charting_type: null,
    video_link: null,
    meshes_based_on_treatment: null,
    comments: null,
  },
];
