/**
 * Debug utility functions for the application
 */

/**
 * Logs the visibility state of all treatments
 * @param {Object} treatmentVisibility - The treatment visibility state object
 */
export const logTreatmentVisibility = (treatmentVisibility) => {
  // Debug function - console logs removed
};

/**
 * Logs all treatments in a model with their visibility status
 * @param {THREE.Object3D} model - The model to inspect
 */
export const logModelTreatments = (model) => {
  // Debug function - console logs removed
};

/**
 * Adds a debug button to the UI
 * @param {Function} onClick - Function to call when the button is clicked
 */
export const addDebugButton = (onClick) => {
  // Create a button element
  const button = document.createElement("button");
  button.textContent = "Debug Treatments";
  button.style.position = "fixed";
  button.style.bottom = "10px";
  button.style.left = "10px";
  button.style.zIndex = "9999";
  button.style.padding = "8px 16px";
  button.style.backgroundColor = "#f44336";
  button.style.color = "white";
  button.style.border = "none";
  button.style.borderRadius = "4px";
  button.style.cursor = "pointer";

  // Add click handler
  button.addEventListener("click", onClick);

  // Add to the document
  document.body.appendChild(button);

  return button;
};
