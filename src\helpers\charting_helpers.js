export const addTreatmentToTooth = (position, treatment) => {
    setPatientTeeth(teeth => teeth.map(tooth => 
        tooth.position_name === position
        ? { ...tooth, treatments: [...tooth.treatments, treatment] }
        : tooth
    ));
};
export const removeTreatmentFromTooth = (position, ctid) => {
    setPatientTeeth(teeth => teeth.map(tooth =>
        tooth.position_name === position
        ? {
            ...tooth,
            treatments: tooth.treatments.filter(t => t.ctid !== ctid)
            }
        : tooth
    ));
};