.tooth-tag {
  pointer-events: none;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
}

.tooth-tag-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid rgba(200, 200, 200, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-out;
  min-width: 120px;
  transform-origin: center bottom;
  opacity: 1;
  visibility: visible;
  font-size: 12px;
}

.tooth-tag.hovered .tooth-tag-content {
  transform: translateY(-5px);
}

.tooth-tag.selected .tooth-tag-content {
  transform: translateY(-8px) scale(1.05);
  min-width: 160px;
}

.tooth-tag-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
}

.tooth-number {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(229, 231, 235, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
  backdrop-filter: blur(4px);
}

.tooth-number span {
  color: #1e40af;
  font-family: monospace;
  font-size: 12px;
  font-weight: bold;
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.tooth-status {
  font-size: 10px;
  font-weight: 500;
  text-transform: capitalize;
  margin-left: auto;
}

.tooth-name {
  color: #1e293b;
  font-size: 12px;
  font-weight: 600;
  margin-top: 4px;
}

.tooth-details {
  margin-top: 12px;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.details-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-card {
  background: rgba(229, 231, 235, 0.8);
  padding: 8px;
  border-radius: 6px;
  backdrop-filter: blur(4px);
}

.detail-label {
  font-size: 10px;
  color: #1e40af;
  font-weight: 500;
  margin-bottom: 2px;
}

.detail-value {
  font-size: 11px;
  color: #1e293b;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: #1e293b;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
}

.history-bullet {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.5);
}

.video-placeholder {
  aspect-ratio: 16/9;
  background: rgba(229, 231, 235, 0.8);
  border-radius: 6px;
  overflow: hidden;
}

.video-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to bottom right, #eff6ff, #f3f4f6);
  padding: 8px;
}

.video-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
}

.video-icon::before {
  content: "";
  width: 8px;
  height: 8px;
  border: 2px solid #3b82f6;
  border-radius: 50%;
}

.video-title {
  font-size: 11px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 2px;
}

.video-subtitle {
  font-size: 9px;
  color: #64748b;
  text-align: center;
}

.tooth-action {
  margin-top: 8px;
  font-size: 10px;
  color: #3b82f6;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
}

.tooth-action:hover {
  color: #2563eb;
}

.tooth-tag-content {
    min-width: 100px;
    max-width: 200px; /* Limit the maximum width */
    width: auto;
  }
  
  .tooth-tag.selected .tooth-tag-content {
    min-width: 340px;
    max-width: 450px;
  }
  .details-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  @media (min-width: 320px) {
    .details-grid {
      grid-template-columns: 1fr 1fr;
    }
  }.tooth-tag-content {
    white-space: normal;
    word-wrap: break-word;
  }