import { takeScreenshot, takeFixedSizeScreenshot, uploadScreenshot } from './screenshotUtils';
import { resetCameraToDefault } from './cameraUtils';

/**
 * Global references for taking screenshots
 */
let rendererRef = null;
let cameraRef = null;
let controlsRef = null;
let currentViewRef = null;

/**
 * Set the references for taking screenshots
 * @param {Object} renderer - The Three.js renderer
 * @param {Object} camera - The Three.js camera
 * @param {Object} controls - The OrbitControls instance
 * @param {string} currentView - The current view mode
 */
export const setScreenshotRenderer = (renderer, camera, controls, currentView) => {
  rendererRef = renderer;
  cameraRef = camera;
  controlsRef = controls;
  currentViewRef = currentView;
};

/**
 * Take a screenshot and upload it to the server
 * @param {string} patientId - The patient ID to associate with the screenshot
 * @returns {Promise<string>} - A promise that resolves to the URL of the uploaded image
 */
export const takeAndUploadScreenshot = async (patientId) => {
  if (!rendererRef) {
    throw new Error('Renderer not initialized. Cannot take screenshot.');
  }

  if (!patientId) {
    throw new Error('Patient ID is required for taking a screenshot.');
  }

  try {

    // Log the current view mode

    // Use the fixed-size screenshot function to ensure consistent dimensions
    let blob;
    if (rendererRef && rendererRef.scene && cameraRef && controlsRef) {
      
      blob = await takeFixedSizeScreenshot(
        rendererRef,
        cameraRef,
        controlsRef,
        rendererRef.scene,
        currentViewRef
      );
    } else {
      // Fallback to the original method if any required references are missing

      // Reset camera to default position if possible
      if (cameraRef && controlsRef && controlsRef.current && currentViewRef) {
        
        resetCameraToDefault(cameraRef, controlsRef.current, currentViewRef);
      }

      // Force a render before taking the screenshot
      if (rendererRef.scene && rendererRef.camera) {
        rendererRef.render(rendererRef.scene, rendererRef.camera);
      }

      // Take the screenshot with the standard method
      blob = await takeScreenshot(rendererRef);
    }

    // Upload the screenshot
    const result = await uploadScreenshot(blob, patientId);

    // Parse the result to get the URL
    let uploadedUrl = '';
    try {
      const resultObj = JSON.parse(result);

      // Check for different response formats
      if (resultObj.data && resultObj.data.length > 0 && resultObj.data[0].Location) {
        // Format from the current API response
        uploadedUrl = resultObj.data[0].Location;
      } else if (resultObj.url) {
        uploadedUrl = resultObj.url;
      } else if (resultObj.imageUrl) {
        uploadedUrl = resultObj.imageUrl;
      } else if (resultObj.fileUrl) {
        uploadedUrl = resultObj.fileUrl;
      } else if (resultObj.data && resultObj.data.url) {
        uploadedUrl = resultObj.data.url;
      }
    } catch (e) {
      // If the result is not JSON, assume it's the URL directly
      uploadedUrl = result;
    }

    return {
      success: true,
      url: uploadedUrl,
      message: 'Screenshot uploaded successfully'
    };
  } catch (error) {
    
    return {
      success: false,
      error: error.message,
      message: 'Failed to take or upload screenshot'
    };
  }
};

/**
 * Initialize the message listener for screenshot requests
 * @param {Object} teethContext - The teeth context
 */
export const initializeScreenshotMessageListener = (teethContext) => {
  const handleMessage = async (event) => {
    // Check if the message is a screenshot request
    if (event.data && event.data.type === 'take_screenshot') {
      try {
        // Get the patient ID from the context or the message
        const patientId = event.data.patientId || teethContext.patientId;

        if (!patientId) {
          throw new Error('Patient ID is required for taking a screenshot.');
        }

        // Take and upload the screenshot
        const result = await takeAndUploadScreenshot(patientId);

        // Send the result back to the parent window
        window.parent.postMessage({
          type: 'screenshot_result',
          success: result.success,
          url: result.url,
          message: result.message,
          patientId: patientId
        }, '*');
      } catch (error) {

        // Send error back to the parent window
        window.parent.postMessage({
          type: 'screenshot_result',
          success: false,
          error: error.message,
          message: 'Failed to take or upload screenshot'
        }, '*');
      }
    }
  };

  // Add the message listener
  window.addEventListener('message', handleMessage);

  // Return a cleanup function
  return () => {
    window.removeEventListener('message', handleMessage);
  };
};
