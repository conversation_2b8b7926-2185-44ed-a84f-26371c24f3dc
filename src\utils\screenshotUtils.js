/**
 * Utility functions for taking screenshots and uploading them
 */

/**
 * Takes a screenshot of a Three.js renderer and returns a Blob
 * @param {THREE.WebGLRenderer} renderer - The Three.js renderer
 * @param {number} width - The width of the screenshot (optional)
 * @param {number} height - The height of the screenshot (optional)
 * @returns {Promise<Blob>} - A promise that resolves to a Blob containing the screenshot
 */
export const takeScreenshot = (renderer, width, height) => {
  return new Promise((resolve) => {
    // If width and height are not provided, use the renderer's size
    const w = width || renderer.domElement.width;
    const h = height || renderer.domElement.height;

    // Force the renderer to render the current scene
    // This ensures we capture the latest state
    if (renderer.render && renderer.scene && renderer.camera) {
      renderer.render(renderer.scene, renderer.camera);
    }

    // Use the renderer's built-in method to get a screenshot if available
    if (typeof renderer.domElement.toBlob === 'function') {
      renderer.domElement.toBlob((blob) => {
        resolve(blob);
      }, 'image/png');
      return;
    }

    // Fallback method: create a new canvas and draw the WebGL content
    const canvas = document.createElement('canvas');
    canvas.width = w;
    canvas.height = h;
    const context = canvas.getContext('2d');

    // Draw the renderer's canvas onto our new canvas
    // Use preserveDrawingBuffer to ensure the WebGL content is captured
    context.drawImage(renderer.domElement, 0, 0, w, h);

    // Convert the canvas to a Blob
    canvas.toBlob((blob) => {
      resolve(blob);
    }, 'image/png');
  });
};

/**
 * Takes a fixed-size screenshot with consistent aspect ratio regardless of device
 * @param {THREE.WebGLRenderer} renderer - The Three.js renderer
 * @param {THREE.Camera} camera - The Three.js camera
 * @param {THREE.Scene} scene - The Three.js scene
 * @param {Object} controls - The OrbitControls instance
 * @param {string} viewMode - The current view mode
 * @returns {Promise<Blob>} - A promise that resolves to a Blob containing the screenshot
 */
export const takeFixedSizeScreenshot = (renderer, camera, controls, scene, viewMode) => {
  return new Promise((resolve) => {
    if (!renderer || !camera || !scene) {
      
      resolve(null);
      return;
    }

    // Store original renderer size and pixel ratio
    const originalWidth = renderer.domElement.width;
    const originalHeight = renderer.domElement.height;
    const originalPixelRatio = renderer.getPixelRatio();
    const originalAspect = camera.aspect;

    // Store original camera properties
    const originalPosition = camera.position.clone();
    const originalFov = camera.fov;
    const originalNear = camera.near;
    const originalFar = camera.far;

    // Store original controls properties if available
    let originalControlsTarget = null;
    let originalControlsEnabled = null;
    if (controls && controls.current) {
      originalControlsTarget = controls.current.target.clone();
      originalControlsEnabled = {
        enableRotate: controls.current.enableRotate,
        enableZoom: controls.current.enableZoom,
        enablePan: controls.current.enablePan
      };

      // Disable controls temporarily
      controls.current.enableRotate = false;
      controls.current.enableZoom = false;
      controls.current.enablePan = false;
    }

    try {
      // Set fixed dimensions for the screenshot (16:9 aspect ratio)
      const fixedWidth = 1920;
      const fixedHeight = 1080;

      // Resize renderer to the fixed size
      renderer.setSize(fixedWidth, fixedHeight, false);
      renderer.setPixelRatio(1);

      // Update camera for the fixed aspect ratio
      camera.aspect = fixedWidth / fixedHeight;

      // Set specific camera settings based on view mode
      if (viewMode === 'charting') {
        // For charting view, use a wider FOV to show all teeth
        camera.fov = 15;
        camera.position.set(0, 0, 0.4);
        if (controls && controls.current) {
          controls.current.target.set(0, 0, 0);
        }
      } else if (viewMode === 'skull' || viewMode === 'jaw') {
        // For skull and jaw views, use standard settings
        camera.fov = 45;
        camera.position.set(0, 0, 0.5);
        if (controls && controls.current) {
          controls.current.target.set(0, 0, 0);
        }
      } else if (viewMode === 'single_treatment') {
        // For single treatment view, zoom in more
        camera.fov = 20;
        camera.position.set(0, 0, 0.4);
        if (controls && controls.current) {
          controls.current.target.set(0, 0, 0);
        }
      }

      // Update camera projection matrix
      camera.updateProjectionMatrix();

      // Update controls if available
      if (controls && controls.current) {
        controls.current.update();
      }

      // Render the scene with the new settings
      renderer.render(scene, camera);

      // Take the screenshot
      if (typeof renderer.domElement.toBlob === 'function') {
        renderer.domElement.toBlob((blob) => {
          // Restore original settings
          renderer.setSize(originalWidth, originalHeight, false);
          renderer.setPixelRatio(originalPixelRatio);

          camera.position.copy(originalPosition);
          camera.fov = originalFov;
          camera.near = originalNear;
          camera.far = originalFar;
          camera.aspect = originalAspect;
          camera.updateProjectionMatrix();

          if (controls && controls.current && originalControlsTarget) {
            controls.current.target.copy(originalControlsTarget);
            if (originalControlsEnabled) {
              controls.current.enableRotate = originalControlsEnabled.enableRotate;
              controls.current.enableZoom = originalControlsEnabled.enableZoom;
              controls.current.enablePan = originalControlsEnabled.enablePan;
            }
            controls.current.update();
          }

          // Render again with original settings
          renderer.render(scene, camera);

          resolve(blob);
        }, 'image/png');
      } else {
        // Fallback method using canvas
        const canvas = document.createElement('canvas');
        canvas.width = fixedWidth;
        canvas.height = fixedHeight;
        const context = canvas.getContext('2d');

        context.drawImage(renderer.domElement, 0, 0, fixedWidth, fixedHeight);

        // Restore original settings
        renderer.setSize(originalWidth, originalHeight, false);
        renderer.setPixelRatio(originalPixelRatio);

        camera.position.copy(originalPosition);
        camera.fov = originalFov;
        camera.near = originalNear;
        camera.far = originalFar;
        camera.aspect = originalAspect;
        camera.updateProjectionMatrix();

        if (controls && controls.current && originalControlsTarget) {
          controls.current.target.copy(originalControlsTarget);
          if (originalControlsEnabled) {
            controls.current.enableRotate = originalControlsEnabled.enableRotate;
            controls.current.enableZoom = originalControlsEnabled.enableZoom;
            controls.current.enablePan = originalControlsEnabled.enablePan;
          }
          controls.current.update();
        }

        // Render again with original settings
        renderer.render(scene, camera);

        canvas.toBlob((blob) => {
          resolve(blob);
        }, 'image/png');
      }
    } catch (error) {

      // Restore original settings in case of error
      renderer.setSize(originalWidth, originalHeight, false);
      renderer.setPixelRatio(originalPixelRatio);

      camera.position.copy(originalPosition);
      camera.fov = originalFov;
      camera.near = originalNear;
      camera.far = originalFar;
      camera.aspect = originalAspect;
      camera.updateProjectionMatrix();

      if (controls && controls.current && originalControlsTarget) {
        controls.current.target.copy(originalControlsTarget);
        if (originalControlsEnabled) {
          controls.current.enableRotate = originalControlsEnabled.enableRotate;
          controls.current.enableZoom = originalControlsEnabled.enableZoom;
          controls.current.enablePan = originalControlsEnabled.enablePan;
        }
        controls.current.update();
      }

      // Render again with original settings
      renderer.render(scene, camera);

      resolve(null);
    }
  });
};

/**
 * Uploads a screenshot to the server
 * @param {Blob} blob - The screenshot blob
 * @param {string} patientId - The patient ID to associate with the screenshot
 * @returns {Promise<string>} - A promise that resolves to the URL of the uploaded image
 */
export const uploadScreenshot = async (blob, patientId) => {
  try {
    // Create a file from the blob with just the patient ID as the name
    // This will overwrite the previous file with the same name
    // Add a folder path to organize screenshots in S3
    const file = new File([blob], `screenshots/${patientId}.png`, { type: 'image/png' });

    // Create form data
    const formData = new FormData();
    formData.append("file", file);

    // Add patient ID as metadata if needed
    if (patientId) {
      formData.append("patientId", patientId);
    }

    // Upload the screenshot
    const response = await fetch("https://api.modularcx.link/upod-medical/s3/upload", {
      method: "POST",
      body: formData,
      redirect: "follow"
    });

    if (!response.ok) {
      throw new Error(`Upload failed with status: ${response.status}`);
    }

    const result = await response.text();

    // Return the result (usually contains the URL of the uploaded image)
    return result;
  } catch (error) {
    
    throw error;
  }
};
