import React from 'react';
import { Html } from "@react-three/drei";
import "./right_click_modal.css";

export function RightClickModal ({ rightClickData, closeRightClickModal, applyMissingTooth, applyWatchTooth, applyMixedDentation, applyResetTooth }) {
// Offset to shift modal slightly (e.g. below/right of cursor)
  const offsetX = 15;
  const offsetY = 10;

  // Clamp modal position to avoid overflowing window
  const clampedX = -window.innerWidth/2 + rightClickData.cursor.x + offsetX;
  const clampedY = -window.innerHeight/2 + rightClickData.cursor.y + offsetY;
  return (
    <Html>
        <div className="modalStyle" style={{
            top: `${clampedY}px`,
            left: `${clampedX}px`,
            transform: 'none',
            }}>
            <div className="closeIconStyle" onClick={closeRightClickModal}>
                ✕
            </div>
            <button className="fullWidthButton">View Tooth History</button>
            <div className="rowButtons">
                <button className="smallButton" onClick={() => applyMissingTooth(rightClickData.tooth)}>Missing Tooth</button>
                <button className="smallButton" onClick={() => applyWatchTooth(rightClickData.tooth)}>Watch Tooth </button>
                <button className="smallButton" onClick={() => applyMixedDentation(rightClickData.tooth)}>Mixed Dentation</button>
                <button className="smallButton" onClick={() => applyResetTooth(rightClickData.tooth)}>Reset Tooth</button>
            </div>
        </div>
    </Html>
  );
};
