import { useRef } from "react";

export function InteractiveSquare({ position }) {
    const ref = useRef();

    // Geometry Data: Outer and Inner Square Vertices
    const size = 0.01/2;  // Outer square size
    const innerSize = size * 0.5; // Inner square is 50% of outer
    const height = 0.001; // Slight elevation to avoid z-fighting

    const vertices = new Float32Array([
        // Outer square
        -size, -size, height,  
         size, -size, height,  
         size,  size, height,  
        -size,  size, height,  

        // Inner square
        -innerSize, -innerSize, height,  
         innerSize, -innerSize, height,  
         innerSize,  innerSize, height,  
        -innerSize,  innerSize, height   
    ]);

    // Faces connecting outer and inner square
    const indices = [
        // Outer square (2 triangles)
        0, 1, 2,  0, 2, 3,
        
        // Inner square (2 triangles)
        4, 5, 6,  4, 6, 7,

        // Connecting trapezoidal faces
        0, 1, 5,  0, 5, 4, // Bottom edge
        1, 2, 6,  1, 6, 5, // Right edge
        2, 3, 7,  2, 7, 6, // Top edge
        3, 0, 4,  3, 4, 7  // Left edge
    ];

    // Click Handler
    const handleClick = (event) => {
        event.stopPropagation();
        
    };

    return (
        <mesh ref={ref} position={position} onClick={handleClick}>
            <bufferGeometry>
                <bufferAttribute
                    attach="attributes-position"
                    array={vertices}
                    count={vertices.length / 3}
                    itemSize={3}
                />
                <bufferAttribute
                    attach="index"
                    array={new Uint16Array(indices)}
                    count={indices.length}
                    itemSize={1}
                />
            </bufferGeometry>
            <meshBasicMaterial color="black" transparent opacity={0.3} side={2} />
            <meshBasicMaterial color="black" wireframe />
        </mesh>
    );
}

export default InteractiveSquare;