import { BASE_URL, TREATMENTS_VERSION } from "../config/api";

// Treatments that have specific _A and _B folders.
// These should match the exact casing of the treatment name used in logic/passed as parameter.
const A_B_SPECIFIC_TREATMENTS = new Set(["Clasp"]);

// Treatments that require the default tooth model to be loaded alongside them.
export const DUAL_LOAD_TREATMENTS = new Set([
  "Clasp",
  "Crown",
  "Bridge",
  "Veneers",
]);

export function getModelUrl(treatmentName, stateKey, patientType) {
  const s3versionForSpecificTreatments = TREATMENTS_VERSION; // e.g., "treatmentsV3"
  const s3versionForAdultGeneric = "treatmentsV2"; // For generic adult skull/jaw

  const stateViewSuffix = stateKey === "STATE_A" ? "A" : "B"; // For "StateA.glb", "ChildStateA.glb"

  // Use a logical name for default/child checks, but original treatmentName for paths
  const logicalName =
    treatmentName && typeof treatmentName === "string"
      ? treatmentName.charAt(0).toUpperCase() +
        treatmentName.slice(1).toLowerCase()
      : "Default";

  // CHILDREN
  if (patientType === "CHILDREN") {
    // Generic child model: if treatmentName is "child", "Child", "default", "Default", or null/undefined
    if (logicalName === "Child" || logicalName === "Default") {
      return `${BASE_URL}/${s3versionForSpecificTreatments}/child/ChildState${stateViewSuffix}.glb`;
    }

    // Specific treatments for CHILDREN (e.g., "Clasp" for a child)
    // Use the raw `treatmentName` for Set check and path construction, assuming it has correct casing.
    if (A_B_SPECIFIC_TREATMENTS.has(treatmentName)) {
      const folderAndFileName = `${treatmentName}_${stateViewSuffix}`;
      return `${BASE_URL}/${s3versionForSpecificTreatments}/${folderAndFileName}/${folderAndFileName}.glb`;
    } else {
      // Standard treatment: FolderName/FolderName.glb
      return `${BASE_URL}/${s3versionForSpecificTreatments}/${treatmentName}/${treatmentName}.glb`;
    }
  }

  // ADULT
  if (patientType === "ADULT") {
    // Generic adult model: if treatmentName is "Default", "Skull", "Jaw", or null/undefined
    if (
      logicalName === "Default" ||
      logicalName === "Skull" ||
      logicalName === "Jaw"
    ) {
      return `${BASE_URL}/${s3versionForAdultGeneric}/State${stateViewSuffix}/State${stateViewSuffix}.glb`;
    }

    // Specific treatments for ADULTS
    // Use the raw `treatmentName` for Set check and path construction.
    if (A_B_SPECIFIC_TREATMENTS.has(treatmentName)) {
      const folderAndFileName = `${treatmentName}_${stateViewSuffix}`;
      return `${BASE_URL}/${s3versionForSpecificTreatments}/${folderAndFileName}/${folderAndFileName}.glb`;
    } else {
      // Standard treatment: FolderName/FolderName.glb
      return `${BASE_URL}/${s3versionForSpecificTreatments}/${treatmentName}/${treatmentName}.glb`;
    }
  }

  return null;
}

export const MODELS = {
  // SKULL and JAW models are now dynamically loaded using getModelUrl.
  // Static models that don't fit the dynamic pattern remain here.
  CHARTING: `${BASE_URL}/states/ChartingV24.glb`,
  WATCHTOOL: `${BASE_URL}/states/ChartingWatchTool.glb`,
};
