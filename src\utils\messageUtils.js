/**
 * Utility functions for handling message communication between components
 * related to the 3D model.
 */

/**
 * Dispatch a teeth-cleared event to notify components that teeth should be cleared
 */
export const dispatchTeethClearedEvent = () => {
  const clearEvent = new CustomEvent('teeth-cleared');
  window.dispatchEvent(clearEvent);

};

/**
 * Dispatch a teeth-initialized event to notify components that teeth have been initialized
 */
export const dispatchTeethInitializedEvent = () => {
  const initEvent = new CustomEvent('teeth-initialized');
  window.dispatchEvent(initEvent);

};

/**
 * Dispatch a tooth-selected event to notify components that a tooth has been selected
 * @param {String} toothNumber - The tooth number
 */
export const dispatchToothSelectedEvent = (toothNumber) => {
  const selectEvent = new CustomEvent('tooth-selected', { detail: { toothNumber } });
  window.dispatchEvent(selectEvent);

};

/**
 * Dispatch a tooth-hovered event to notify components that a tooth is being hovered
 * @param {String} toothNumber - The tooth number
 */
export const dispatchToothHoveredEvent = (toothNumber) => {
  const hoverEvent = new CustomEvent('tooth-hovered', { detail: { toothNumber } });
  window.dispatchEvent(hoverEvent);

};

/**
 * Dispatch a treatment-applied event to notify components that a treatment has been applied
 * @param {String} toothNumber - The tooth number
 * @param {Object} treatment - The treatment object
 */
export const dispatchTreatmentAppliedEvent = (toothNumber, treatment) => {
  const treatmentEvent = new CustomEvent('treatment-applied', {
    detail: { toothNumber, treatment }
  });
  window.dispatchEvent(treatmentEvent);

};

/**
 * Add an event listener for teeth-cleared events
 * @param {Function} callback - The callback function to execute when the event is triggered
 * @returns {Function} - A function to remove the event listener
 */
export const addTeethClearedListener = (callback) => {
  const handler = () => {

    callback();
  };

  window.addEventListener('teeth-cleared', handler);

  return () => {
    window.removeEventListener('teeth-cleared', handler);
  };
};

/**
 * Add an event listener for teeth-initialized events
 * @param {Function} callback - The callback function to execute when the event is triggered
 * @returns {Function} - A function to remove the event listener
 */
export const addTeethInitializedListener = (callback) => {
  const handler = () => {

    callback();
  };

  window.addEventListener('teeth-initialized', handler);

  return () => {
    window.removeEventListener('teeth-initialized', handler);
  };
};

/**
 * Add an event listener for tooth-selected events
 * @param {Function} callback - The callback function to execute when the event is triggered
 * @returns {Function} - A function to remove the event listener
 */
export const addToothSelectedListener = (callback) => {
  const handler = (event) => {

    callback(event.detail.toothNumber);
  };

  window.addEventListener('tooth-selected', handler);

  return () => {
    window.removeEventListener('tooth-selected', handler);
  };
};

/**
 * Add an event listener for tooth-hovered events
 * @param {Function} callback - The callback function to execute when the event is triggered
 * @returns {Function} - A function to remove the event listener
 */
export const addToothHoveredListener = (callback) => {
  const handler = (event) => {

    callback(event.detail.toothNumber);
  };

  window.addEventListener('tooth-hovered', handler);

  return () => {
    window.removeEventListener('tooth-hovered', handler);
  };
};

/**
 * Add an event listener for treatment-applied events
 * @param {Function} callback - The callback function to execute when the event is triggered
 * @returns {Function} - A function to remove the event listener
 */
export const addTreatmentAppliedListener = (callback) => {
  const handler = (event) => {

    callback(event.detail.toothNumber, event.detail.treatment);
  };

  window.addEventListener('treatment-applied', handler);

  return () => {
    window.removeEventListener('treatment-applied', handler);
  };
};
